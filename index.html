<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smooth Color Transition on Scroll</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-left">
                <a href="#" class="nav-link">Home</a>
                <a href="#" class="nav-link">About</a>
                <a href="#" class="nav-link">Services</a>
                <a href="#" class="nav-link">Contact</a>
            </div>
            <div class="nav-center">
                <h1 class="brand-name">Parfumerie Charme</h1>
                <div class="brand-search-icon" id="searchIcon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="M21 21l-4.35-4.35"></path>
                    </svg>
                </div>
            </div>
            <div class="nav-right">
                <div class="language-changer">
                    <div class="language-selector">
                        <span class="current-lang" id="current-lang">
                            <span class="flag">
                                <svg width="20" height="14" viewBox="0 0 20 14" xmlns="http://www.w3.org/2000/svg">
                                    <!-- US Flag -->
                                    <rect width="20" height="14" fill="#B22234"/>
                                    <rect y="1" width="20" height="1" fill="#FFFFFF"/>
                                    <rect y="3" width="20" height="1" fill="#FFFFFF"/>
                                    <rect y="5" width="20" height="1" fill="#FFFFFF"/>
                                    <rect y="7" width="20" height="1" fill="#FFFFFF"/>
                                    <rect y="9" width="20" height="1" fill="#FFFFFF"/>
                                    <rect y="11" width="20" height="1" fill="#FFFFFF"/>
                                    <rect y="13" width="20" height="1" fill="#FFFFFF"/>
                                    <rect width="8" height="7" fill="#3C3B6E"/>
                                </svg>
                            </span>
                            <span class="lang-code">ENG</span>
                        </span>
                        <div class="language-dropdown" id="language-dropdown">
                            <div class="lang-option" data-lang="en" data-code="ENG">
                                <span class="flag">
                                    <svg width="18" height="12" viewBox="0 0 20 14" xmlns="http://www.w3.org/2000/svg">
                                        <!-- US Flag -->
                                        <rect width="20" height="14" fill="#B22234"/>
                                        <rect y="1" width="20" height="1" fill="#FFFFFF"/>
                                        <rect y="3" width="20" height="1" fill="#FFFFFF"/>
                                        <rect y="5" width="20" height="1" fill="#FFFFFF"/>
                                        <rect y="7" width="20" height="1" fill="#FFFFFF"/>
                                        <rect y="9" width="20" height="1" fill="#FFFFFF"/>
                                        <rect y="11" width="20" height="1" fill="#FFFFFF"/>
                                        <rect y="13" width="20" height="1" fill="#FFFFFF"/>
                                        <rect width="8" height="7" fill="#3C3B6E"/>
                                    </svg>
                                </span>
                                <span class="lang-name">English</span>
                            </div>
                            <div class="lang-option" data-lang="fr" data-code="FR">
                                <span class="flag">
                                    <svg width="18" height="12" viewBox="0 0 20 14" xmlns="http://www.w3.org/2000/svg">
                                        <!-- French Flag -->
                                        <rect width="6.67" height="14" fill="#002395"/>
                                        <rect x="6.67" width="6.67" height="14" fill="#FFFFFF"/>
                                        <rect x="13.33" width="6.67" height="14" fill="#ED2939"/>
                                    </svg>
                                </span>
                                <span class="lang-name">Français</span>
                            </div>
                            <div class="lang-option" data-lang="ar" data-code="ARB">
                                <span class="flag">
                                    <svg width="18" height="12" viewBox="0 0 20 14" xmlns="http://www.w3.org/2000/svg">
                                        <!-- Saudi Arabia Flag -->
                                        <rect width="20" height="14" fill="#006C35"/>
                                        <text x="10" y="9" font-family="Arial" font-size="3" fill="#FFFFFF" text-anchor="middle">العربية</text>
                                    </svg>
                                </span>
                                <span class="lang-name">العربية</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Search Modal -->
    <div id="searchModal" class="search-modal">
        <div class="search-modal-content">
            <div class="search-header">
                <h2>Search Fragrances</h2>
                <button class="search-close" id="searchClose">&times;</button>
            </div>
            <div class="search-input-container">
                <input type="text" id="searchInput" placeholder="Search for perfumes, brands, notes..." autocomplete="off">
                <div class="search-icon-input">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="11" cy="11" r="8"></circle>
                        <path d="M21 21l-4.35-4.35"></path>
                    </svg>
                </div>
            </div>
            <div class="search-results" id="searchResults">
                <div class="search-suggestions">
                    <h3>Popular Searches</h3>
                    <div class="suggestion-tags">
                        <span class="suggestion-tag">Layton</span>
                        <span class="suggestion-tag">Parfums de Marly</span>
                        <span class="suggestion-tag">Woody</span>
                        <span class="suggestion-tag">Fresh</span>
                        <span class="suggestion-tag">Oriental</span>
                        <span class="suggestion-tag">Citrus</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <section class="hero">
            <video id="background-video" autoplay muted loop>
                <source src="background.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <video id="background-video-2" autoplay muted loop style="display: none;">
                <source src="background2.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>
            <video id="background-video-3" autoplay muted loop style="display: none;">
                <source src="background3.mp4" type="video/mp4">
                Your browser does not support the video tag.
            </video>

            <!-- Video Controls -->
            <div class="video-controls">
                <button class="video-control-btn" id="play-pause-btn">
                    <svg class="play-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="6,4 20,12 6,20"></polygon>
                    </svg>
                    <svg class="pause-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="display: none;">
                        <rect x="6" y="4" width="4" height="16"></rect>
                        <rect x="14" y="4" width="4" height="16"></rect>
                    </svg>
                </button>
                <button class="video-control-btn" id="next-video-btn">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="5,4 15,12 5,20"></polygon>
                        <line x1="19" y1="5" x2="19" y2="19"></line>
                    </svg>
                </button>
            </div>
        </section>

        <!-- Parfums de Marly Brand Image -->
        <div class="brand-image-section">
            <img src="parfumsdemarly.jpg" alt="Parfums de Marly" class="brand-image">
        </div>

        <section class="content">
            <img src="layton.png" alt="Layton Perfume" class="layton-image">
            <div class="product-title">
                <h1 class="brand-name">PARFUMS de MARLY</h1>
                <h2 class="brand-location">PARIS</h2>
                <h3 class="product-name">Layton</h3>
                <div class="product-price-container">
                    <div class="price-badge">
                        <div class="price-ornament top-left"></div>
                        <div class="price-ornament top-right"></div>
                        <div class="price-ornament bottom-left"></div>
                        <div class="price-ornament bottom-right"></div>
                        <div class="price-shimmer"></div>
                        <div class="product-price">
                            <span class="price-currency">35</span>
                            <span class="price-unit">dt</span>
                        </div>
                        <div class="price-glow"></div>
                    </div>
                    <div class="price-subtitle">Premium Collection</div>
                </div>

                <div class="quality-selector-container">
                    <div class="quality-header">
                        <h4 class="quality-title">Select Quality</h4>
                        <div class="quality-subtitle">Choose Your Preference</div>
                    </div>

                    <div class="quality-options">
                        <div class="quality-option" data-quality="top">
                            <input type="radio" id="top-quality" name="quality" value="top" checked>
                            <label for="top-quality" class="quality-label">
                                <div class="quality-badge">
                                    <div class="quality-ornament top-left"></div>
                                    <div class="quality-ornament top-right"></div>
                                    <div class="quality-ornament bottom-left"></div>
                                    <div class="quality-ornament bottom-right"></div>
                                    <div class="quality-shimmer"></div>
                                    <div class="quality-content">
                                        <div class="quality-name">Top Quality</div>
                                        <div class="quality-description">Premium oil formulation</div>
                                    </div>
                                    <div class="quality-glow"></div>
                                    <div class="selection-indicator"></div>
                                </div>
                            </label>
                        </div>

                        <div class="quality-option" data-quality="identical">
                            <input type="radio" id="identical-quality" name="quality" value="identical">
                            <label for="identical-quality" class="quality-label">
                                <div class="quality-badge">
                                    <div class="quality-ornament top-left"></div>
                                    <div class="quality-ornament top-right"></div>
                                    <div class="quality-ornament bottom-left"></div>
                                    <div class="quality-ornament bottom-right"></div>
                                    <div class="quality-shimmer"></div>
                                    <div class="quality-content">
                                        <div class="quality-name">Identical Quality</div>
                                        <div class="quality-description">High-quality alternative</div>
                                    </div>
                                    <div class="quality-glow"></div>
                                    <div class="selection-indicator"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="layton-notes custom-fragrance-profile">
                <div class="fragrance-profile-container">
                    <div class="profile-header">
                        <h3 class="profile-title">Scent Profile</h3>
                        <div class="profile-subtitle">Olfactory Characteristics</div>
                    </div>

                    <div class="scent-categories">
                        <div class="category-item warm-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 85%"></div>
                            </div>
                            <span class="category-label">warm spicy</span>
                        </div>

                        <div class="category-item fresh-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 72%"></div>
                            </div>
                            <span class="category-label">fresh spicy</span>
                        </div>

                        <div class="category-item woody">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 68%"></div>
                            </div>
                            <span class="category-label">woody</span>
                        </div>

                        <div class="category-item vanilla">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 78%"></div>
                            </div>
                            <span class="category-label">vanilla</span>
                        </div>

                        <div class="category-item aromatic">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 65%"></div>
                            </div>
                            <span class="category-label">aromatic</span>
                        </div>

                        <div class="category-item fruity">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 58%"></div>
                            </div>
                            <span class="category-label">fruity</span>
                        </div>

                        <div class="category-item powdery">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 45%"></div>
                            </div>
                            <span class="category-label">powdery</span>
                        </div>

                        <div class="category-item lavender">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 52%"></div>
                            </div>
                            <span class="category-label">lavender</span>
                        </div>

                        <div class="category-item citrus">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 42%"></div>
                            </div>
                            <span class="category-label">citrus</span>
                        </div>

                        <div class="category-item fresh">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 38%"></div>
                            </div>
                            <span class="category-label">fresh</span>
                        </div>
                    </div>

                    <div class="intensity-meter">
                        <div class="meter-label">Overall Intensity</div>
                        <div class="meter-container">
                            <div class="meter-track">
                                <div class="meter-fill" style="width: 76%"></div>
                                <div class="meter-indicator" style="left: 76%"></div>
                            </div>
                            <div class="meter-scale">
                                <span class="scale-mark">Light</span>
                                <span class="scale-mark">Moderate</span>
                                <span class="scale-mark">Strong</span>
                                <span class="scale-mark">Beast Mode</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="fragrance-notes">
                <div class="notes-section">
                    <div class="notes-row">
                        <div class="note-item">
                            <div class="note-icon">🍎</div>
                            <span class="note-name">Apple</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🪻</div>
                            <span class="note-name">Lavender</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🍋</div>
                            <span class="note-name">Bergamot</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🍊</div>
                            <span class="note-name">Mandarin Orange</span>
                        </div>
                    </div>

                    <h3 class="notes-title">Middle Notes</h3>
                    <div class="notes-row">
                        <div class="note-item">
                            <div class="note-icon">🌺</div>
                            <span class="note-name">Geranium</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🟣</div>
                            <span class="note-name">Violet</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🌸</div>
                            <span class="note-name">Jasmine</span>
                        </div>
                    </div>

                    <h3 class="notes-title">Base Notes</h3>
                    <div class="notes-row">
                        <div class="note-item">
                            <div class="note-icon">🍦</div>
                            <span class="note-name">Vanilla</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🌿</div>
                            <span class="note-name">Cardamom</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🪵</div>
                            <span class="note-name">Sandalwood</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🌶️</div>
                            <span class="note-name">Pepper</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🪨</div>
                            <span class="note-name">Guaiac Wood</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🍃</div>
                            <span class="note-name">Patchouli</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Rating Sections -->
            <div class="additional-ratings">
                <!-- Longevity and Sillage Row -->
                <div class="rating-row">
                    <div class="rating-category">
                        <div class="category-header">
                            <div class="category-icon">⏱️</div>
                            <h4 class="category-title">LONGEVITY</h4>
                            <span class="no-vote">no vote</span>
                        </div>
                        <div class="rating-bars">
                            <div class="rating-item">
                                <span class="rating-label">very weak</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 8%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">180</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">weak</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 14%; background-color: #ff8e53;"></div>
                                </div>
                                <span class="rating-count">305</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">moderate</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 35%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">2197</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">long lasting</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 65%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">6195</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">eternal</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 20%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">1203</span>
                            </div>
                        </div>
                    </div>

                    <div class="rating-category">
                        <div class="category-header">
                            <div class="category-icon">🌊</div>
                            <h4 class="category-title">SILLAGE</h4>
                            <span class="no-vote">no vote</span>
                        </div>
                        <div class="rating-bars">
                            <div class="rating-item">
                                <span class="rating-label">intimate</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 12%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">504</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">moderate</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 45%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">3846</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">strong</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 60%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">5053</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">enormous</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 18%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">836</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gender and Price Value Row -->
                <div class="rating-row">
                    <div class="rating-category">
                        <div class="category-header">
                            <div class="category-icon">⚧️</div>
                            <h4 class="category-title">GENDER</h4>
                            <span class="no-vote">no vote</span>
                        </div>
                        <div class="gender-indicators">
                            <div class="gender-scale">
                                <div class="gender-labels">
                                    <span>female</span>
                                    <span>unisex</span>
                                    <span>male</span>
                                </div>
                                <div class="gender-dots">
                                    <div class="gender-dot"></div>
                                    <div class="gender-dot active"></div>
                                    <div class="gender-dot"></div>
                                    <div class="gender-dot active"></div>
                                    <div class="gender-dot active"></div>
                                </div>
                            </div>
                        </div>
                        <div class="rating-bars">
                            <div class="rating-item">
                                <span class="rating-label">female</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 8%; background-color: #ff8e53;"></div>
                                </div>
                                <span class="rating-count">97</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">more female</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 5%; background-color: #ff8e53;"></div>
                                </div>
                                <span class="rating-count">53</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">unisex</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 25%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">1298</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">more male</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 55%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">4266</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">male</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 45%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">3342</span>
                            </div>
                        </div>
                    </div>

                    <div class="rating-category">
                        <div class="category-header">
                            <div class="category-icon">💰</div>
                            <h4 class="category-title">PRICE VALUE</h4>
                            <span class="no-vote">no vote</span>
                        </div>
                        <div class="price-indicators">
                            <div class="price-scale">
                                <div class="price-labels">
                                    <span>$$$$$</span>
                                    <span>$$$</span>
                                    <span>$</span>
                                </div>
                            </div>
                        </div>
                        <div class="rating-bars">
                            <div class="rating-item">
                                <span class="rating-label">way overpriced</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 35%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">1464</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">overpriced</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 55%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">3245</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">ok</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 50%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">3093</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">good value</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 15%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">529</span>
                            </div>
                            <div class="rating-item">
                                <span class="rating-label">great value</span>
                                <div class="rating-bar">
                                    <div class="bar-fill" style="width: 8%; background-color: #ff6b6b;"></div>
                                </div>
                                <span class="rating-count">172</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reddit Review Section -->
            <div class="reddit-review-section">
                <div class="reddit-review-card">
                    <div class="reddit-header">
                        <div class="reddit-logo">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <circle cx="10" cy="10" r="10" fill="#FF4500"/>
                                <path d="M16.5 10c0-.83-.67-1.5-1.5-1.5-.4 0-.77.16-1.04.42-1.02-.74-2.43-1.21-3.96-1.27l.67-3.17 2.19.46c.02.58.49 1.06 1.08 1.06.6 0 1.08-.48 1.08-1.08S14.54 4.34 13.94 4.34c-.42 0-.78.24-.96.59l-2.44-.52c-.06-.01-.12 0-.17.03-.05.03-.08.08-.09.14L9.6 7.68c-1.56.06-2.99.53-4.02 1.28-.27-.26-.64-.42-1.04-.42-.83 0-1.5.67-1.5 1.5 0 .65.42 1.2 1 1.41-.02.2-.03.4-.03.61 0 3.11 3.62 5.64 8.09 5.64s8.09-2.53 8.09-5.64c0-.21-.01-.41-.03-.61.58-.21 1-.76 1-1.41z" fill="white"/>
                            </svg>
                        </div>
                        <div class="reddit-info">
                            <span class="reddit-platform">Reddit</span>
                            <span class="reddit-subreddit">r/fragrance</span>
                        </div>
                        <div class="reddit-votes">
                            <div class="upvote-icon">▲</div>
                            <span class="vote-count">2.4k</span>
                        </div>
                    </div>

                    <div class="reddit-content">
                        <div class="reddit-user">
                            <div class="user-avatar">
                                <img src="data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23FF6B35'/%3E%3Cpath d='M20 8c-6.627 0-12 5.373-12 12s5.373 12 12 12 12-5.373 12-12S26.627 8 20 8zm0 6c1.657 0 3 1.343 3 3s-1.343 3-3 3-3-1.343-3-3 1.343-3 3-3zm0 18c-3.315 0-6.168-1.69-7.848-4.256.024-2.598 5.216-4.024 7.848-4.024s7.824 1.426 7.848 4.024C26.168 30.31 23.315 32 20 32z' fill='white'/%3E%3C/svg%3E" alt="User Avatar">
                            </div>
                            <div class="user-details">
                                <span class="username">u/FragranceEnthusiast92</span>
                                <span class="post-time">3 months ago</span>
                            </div>
                        </div>

                        <div class="reddit-text">
                            <p>"Just tried Layton for the first time and WOW! The apple and vanilla combo is absolutely incredible. Got compliments all day at work. This is definitely going on my full bottle wishlist. The longevity is insane - still smelling it 8 hours later!"</p>
                        </div>

                        <div class="reddit-engagement">
                            <div class="engagement-item">
                                <span class="engagement-icon">💬</span>
                                <span class="engagement-count">156 comments</span>
                            </div>
                            <div class="engagement-item">
                                <span class="engagement-icon">🔗</span>
                                <span class="engagement-text">Share</span>
                            </div>
                            <div class="engagement-item">
                                <span class="engagement-icon">⭐</span>
                                <span class="engagement-text">Save</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="perfume-rating">
                <div class="rating-indicators">
                    <div class="mood-indicators">
                        <div class="indicator-item active">
                            <div class="indicator-icon">😍</div>
                            <span class="indicator-label">love</span>
                            <div class="indicator-bar"></div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-icon">😊</div>
                            <span class="indicator-label">like</span>
                            <div class="indicator-bar"></div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-icon">😐</div>
                            <span class="indicator-label">ok</span>
                            <div class="indicator-bar"></div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-icon">😞</div>
                            <span class="indicator-label">dislike</span>
                            <div class="indicator-bar"></div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-icon">😠</div>
                            <span class="indicator-label">hate</span>
                            <div class="indicator-bar"></div>
                        </div>
                    </div>

                    <div class="season-indicators">
                        <div class="indicator-item active">
                            <div class="indicator-icon">❄️</div>
                            <span class="indicator-label">winter</span>
                            <div class="indicator-bar winter-bar"></div>
                        </div>
                        <div class="indicator-item active">
                            <div class="indicator-icon">🌱</div>
                            <span class="indicator-label">spring</span>
                            <div class="indicator-bar spring-bar"></div>
                        </div>
                        <div class="indicator-item">
                            <div class="indicator-icon">🏖️</div>
                            <span class="indicator-label">summer</span>
                            <div class="indicator-bar summer-bar"></div>
                        </div>
                        <div class="indicator-item active">
                            <div class="indicator-icon">🍂</div>
                            <span class="indicator-label">fall</span>
                            <div class="indicator-bar fall-bar"></div>
                        </div>
                        <div class="indicator-item active">
                            <div class="indicator-icon">☀️</div>
                            <span class="indicator-label">day</span>
                            <div class="indicator-bar day-bar"></div>
                        </div>
                        <div class="indicator-item active">
                            <div class="indicator-icon">🌙</div>
                            <span class="indicator-label">night</span>
                            <div class="indicator-bar night-bar"></div>
                        </div>
                    </div>
                </div>

                <div class="rating-score">
                    <h3 class="rating-title">Perfume rating <span class="score">4.45</span> out of 5 with <span class="votes">14,856</span> votes</h3>
                </div>

                <div class="perfume-description">
                    <p><strong>Layton</strong> by <strong>Parfums de Marly</strong> is a Oriental Floral fragrance for women and men. <strong>Layton</strong> was launched in 2016. The nose behind this fragrance is Hamid Merati-Kashani. Top notes are Apple, Lavender, Bergamot and Mandarin Orange; middle notes are Geranium, Violet and Jasmine; base notes are Vanilla, Cardamom, Sandalwood, Pepper, Guaiac Wood and Patchouli.</p>
                </div>


            </div>
        </section>
        <!-- Parfums de Marly Haltane Section -->
        <section class="content haltane-section">
            <img src="haltane.png" alt="Haltane Perfume" class="layton-image">
            <div class="product-title">
                <h1 class="brand-name">PARFUMS de MARLY</h1>
                <h2 class="brand-location">PARIS</h2>
                <h3 class="product-name">Haltane</h3>
                <div class="product-price-container">
                    <div class="price-badge">
                        <div class="price-ornament top-left"></div>
                        <div class="price-ornament top-right"></div>
                        <div class="price-ornament bottom-left"></div>
                        <div class="price-ornament bottom-right"></div>
                        <div class="price-shimmer"></div>
                        <div class="product-price">
                            <span class="price-currency">40</span>
                            <span class="price-unit">dt</span>
                        </div>
                        <div class="price-glow"></div>
                    </div>
                    <div class="price-subtitle">Premium Collection</div>
                </div>

                <div class="quality-selector-container">
                    <div class="quality-header">
                        <h4 class="quality-title">Select Quality</h4>
                        <div class="quality-subtitle">Choose Your Preference</div>
                    </div>

                    <div class="quality-options">
                        <div class="quality-option" data-quality="top">
                            <input type="radio" id="haltane-top-quality" name="haltane-quality" value="top" checked>
                            <label for="haltane-top-quality" class="quality-label">
                                <div class="quality-badge">
                                    <div class="quality-ornament top-left"></div>
                                    <div class="quality-ornament top-right"></div>
                                    <div class="quality-ornament bottom-left"></div>
                                    <div class="quality-ornament bottom-right"></div>
                                    <div class="quality-shimmer"></div>
                                    <div class="quality-content">
                                        <div class="quality-name">Top Quality</div>
                                        <div class="quality-description">Premium oil formulation</div>
                                    </div>
                                    <div class="quality-glow"></div>
                                    <div class="selection-indicator"></div>
                                </div>
                            </label>
                        </div>

                        <div class="quality-option" data-quality="identical">
                            <input type="radio" id="haltane-identical-quality" name="haltane-quality" value="identical">
                            <label for="haltane-identical-quality" class="quality-label">
                                <div class="quality-badge">
                                    <div class="quality-ornament top-left"></div>
                                    <div class="quality-ornament top-right"></div>
                                    <div class="quality-ornament bottom-left"></div>
                                    <div class="quality-ornament bottom-right"></div>
                                    <div class="quality-shimmer"></div>
                                    <div class="quality-content">
                                        <div class="quality-name">Identical Quality</div>
                                        <div class="quality-description">High-quality alternative</div>
                                    </div>
                                    <div class="quality-glow"></div>
                                    <div class="selection-indicator"></div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layton-notes custom-fragrance-profile">
                <div class="fragrance-profile-container">
                    <div class="profile-header">
                        <h3 class="profile-title">Scent Profile</h3>
                        <div class="profile-subtitle">Olfactory Characteristics</div>
                    </div>

                    <div class="scent-categories">
                        <div class="category-item oud">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 95%"></div>
                            </div>
                            <span class="category-label">oud</span>
                        </div>

                        <div class="category-item aromatic">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 85%"></div>
                            </div>
                            <span class="category-label">aromatic</span>
                        </div>

                        <div class="category-item sweet">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 80%"></div>
                            </div>
                            <span class="category-label">sweet</span>
                        </div>

                        <div class="category-item warm-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 75%"></div>
                            </div>
                            <span class="category-label">warm spicy</span>
                        </div>

                        <div class="category-item woody">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 70%"></div>
                            </div>
                            <span class="category-label">woody</span>
                        </div>

                        <div class="category-item lavender">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 60%"></div>
                            </div>
                            <span class="category-label">lavender</span>
                        </div>

                        <div class="category-item fresh-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 55%"></div>
                            </div>
                            <span class="category-label">fresh spicy</span>
                        </div>

                        <div class="category-item metallic">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 50%"></div>
                            </div>
                            <span class="category-label">metallic</span>
                        </div>

                        <div class="category-item leather">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 45%"></div>
                            </div>
                            <span class="category-label">leather</span>
                        </div>

                        <div class="category-item soft-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 40%"></div>
                            </div>
                            <span class="category-label">soft spicy</span>
                        </div>
                    </div>

                    <div class="intensity-meter">
                        <div class="meter-label">Overall Intensity</div>
                        <div class="meter-container">
                            <div class="meter-track">
                                <div class="meter-fill" style="width: 85%"></div>
                                <div class="meter-indicator" style="left: 85%"></div>
                            </div>
                            <div class="meter-scale">
                                <span class="scale-mark">Light</span>
                                <span class="scale-mark">Moderate</span>
                                <span class="scale-mark">Strong</span>
                                <span class="scale-mark">Beast Mode</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Haltane Fragrance Notes Section -->
            <div class="fragrance-notes">
                <div class="notes-section">
                    <h3 class="notes-title">Top Notes</h3>
                    <div class="notes-row">
                        <div class="note-item">
                            <div class="note-icon">🌿</div>
                            <span class="note-name">Clary Sage</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🪻</div>
                            <span class="note-name">Lavender</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🍋</div>
                            <span class="note-name">Bergamot</span>
                        </div>
                    </div>

                    <h3 class="notes-title">Middle Notes</h3>
                    <div class="notes-row">
                        <div class="note-item">
                            <div class="note-icon">🌶️</div>
                            <span class="note-name">Saffron</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🍯</div>
                            <span class="note-name">Praline</span>
                        </div>
                    </div>

                    <h3 class="notes-title">Base Notes</h3>
                    <div class="notes-row">
                        <div class="note-item">
                            <div class="note-icon">🪵</div>
                            <span class="note-name">Agarwood (Oud)</span>
                        </div>
                        <div class="note-item">
                            <div class="note-icon">🌲</div>
                            <span class="note-name">Cedar</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="layton-notes custom-fragrance-profile">
                <div class="fragrance-profile-container">
                    <div class="profile-header">
                        <h3 class="profile-title">Scent Profile</h3>
                        <div class="profile-subtitle">Olfactory Characteristics</div>
                    </div>

                    <div class="scent-categories">
                        <div class="category-item oud">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 95%"></div>
                            </div>
                            <span class="category-label">oud</span>
                        </div>

                        <div class="category-item aromatic">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 85%"></div>
                            </div>
                            <span class="category-label">aromatic</span>
                        </div>

                        <div class="category-item sweet">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 80%"></div>
                            </div>
                            <span class="category-label">sweet</span>
                        </div>

                        <div class="category-item warm-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 75%"></div>
                            </div>
                            <span class="category-label">warm spicy</span>
                        </div>

                        <div class="category-item woody">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 70%"></div>
                            </div>
                            <span class="category-label">woody</span>
                        </div>

                        <div class="category-item lavender">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 60%"></div>
                            </div>
                            <span class="category-label">lavender</span>
                        </div>

                        <div class="category-item fresh-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 55%"></div>
                            </div>
                            <span class="category-label">fresh spicy</span>
                        </div>

                        <div class="category-item metallic">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 50%"></div>
                            </div>
                            <span class="category-label">metallic</span>
                        </div>

                        <div class="category-item leather">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 45%"></div>
                            </div>
                            <span class="category-label">leather</span>
                        </div>

                        <div class="category-item soft-spicy">
                            <div class="category-bar">
                                <div class="category-fill" style="width: 40%"></div>
                            </div>
                            <span class="category-label">soft spicy</span>
                        </div>
                    </div>

                    <div class="intensity-section">
                        <div class="intensity-header">
                            <h4 class="intensity-title">Overall Intensity</h4>
                            <div class="intensity-subtitle">Performance Level</div>
                        </div>
                        <div class="intensity-scale">
                            <span class="intensity-label">Light</span>
                            <div class="intensity-bar">
                                <div class="intensity-fill" style="width: 75%"></div>
                                <div class="intensity-marker" style="left: 75%"></div>
                            </div>
                            <span class="intensity-label">Beast Mode</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="content"></section>
        <section class="content"></section>
        <section class="content"></section>
        <section class="content final"></section>
    </div>

    <!-- Floating Search Icon -->
    <button id="floatingSearch" class="floating-search" aria-label="Search fragrances">
        <svg class="floating-search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
        </svg>
    </button>

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top" aria-label="Back to top">
        <svg class="back-to-top-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="m18 15-6-6-6 6"/>
        </svg>
        <div class="back-to-top-progress">
            <svg class="progress-ring" width="60" height="60">
                <circle class="progress-ring-circle" cx="30" cy="30" r="26" fill="transparent" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
                <circle class="progress-ring-progress" cx="30" cy="30" r="26" fill="transparent" stroke="rgba(255,255,255,0.8)" stroke-width="2"/>
            </svg>
        </div>
    </button>

    <script src="script.js"></script>

    <script>
        // Enhanced Quality selector functionality for multiple fragrance sections
        document.addEventListener('DOMContentLoaded', function() {

            function setupQualitySelector(sectionSelector, radioName, priceMapping) {
                const sections = document.querySelectorAll(sectionSelector);

                sections.forEach(section => {
                    const qualityOptions = section.querySelectorAll(`input[name="${radioName}"]`);
                    const priceBadge = section.querySelector('.price-badge');
                    const priceNumber = section.querySelector('.price-currency');

                    if (!qualityOptions.length || !priceBadge || !priceNumber) return;

                    function animatePrice(startPrice, endPrice, duration = 1000) {
                        const startTime = performance.now();

                        function updatePrice(currentTime) {
                            const elapsed = currentTime - startTime;
                            const progress = Math.min(elapsed / duration, 1);

                            // Easing function for smooth animation
                            const easeOutQuart = 1 - Math.pow(1 - progress, 4);

                            const currentPrice = startPrice + (endPrice - startPrice) * easeOutQuart;
                            priceNumber.textContent = Math.round(currentPrice);

                            if (progress < 1) {
                                requestAnimationFrame(updatePrice);
                            }
                        }

                        requestAnimationFrame(updatePrice);
                    }

                    function updatePriceAndColor(selectedValue) {
                        // Remove existing color classes
                        priceBadge.classList.remove('price-green', 'price-gold');

                        // Get current price
                        const currentPrice = parseInt(priceNumber.textContent);

                        // Add appropriate color class and animate price
                        if (priceMapping[selectedValue]) {
                            const { color, price } = priceMapping[selectedValue];
                            priceBadge.classList.add(color);

                            if (currentPrice !== price) {
                                animatePrice(currentPrice, price, 800);
                            }
                        }
                    }

                    // Set initial color and price based on checked option
                    const checkedOption = section.querySelector(`input[name="${radioName}"]:checked`);
                    if (checkedOption) {
                        updatePriceAndColor(checkedOption.value);
                    }

                    // Add event listeners to quality options
                    qualityOptions.forEach(option => {
                        option.addEventListener('change', function() {
                            if (this.checked) {
                                updatePriceAndColor(this.value);
                            }
                        });
                    });
                });
            }

            // Setup for main Layton section
            setupQualitySelector('.content:first-of-type', 'quality', {
                'top': { color: 'price-green', price: 35 },
                'identical': { color: 'price-gold', price: 50 }
            });

            // Setup for Haltane section
            setupQualitySelector('.content:nth-of-type(2)', 'haltane-quality', {
                'top': { color: 'price-green', price: 40 },
                'identical': { color: 'price-gold', price: 60 }
            });
        });
    </script>
</body>
</html>