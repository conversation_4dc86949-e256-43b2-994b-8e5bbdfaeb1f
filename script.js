document.addEventListener('DOMContentLoaded', function() {
    const body = document.body;
    const video = document.getElementById('background-video');
    const navbar = document.querySelector('.navbar');
    let ticking = false;

    // Search Modal Functionality
    const searchIcon = document.getElementById('searchIcon');
    const floatingSearch = document.getElementById('floatingSearch');
    const searchModal = document.getElementById('searchModal');
    const searchClose = document.getElementById('searchClose');
    const searchInput = document.getElementById('searchInput');
    const searchResults = document.getElementById('searchResults');

    // Sample fragrance data for search
    const fragrances = [
        { name: '<PERSON>', brand: 'Parfums de Marly', notes: ['Apple', 'Lavender', 'Geranium', 'Vanilla', 'Cardamom'], type: 'Oriental Woody' },
        { name: 'Hero<PERSON>', brand: 'Parfums de <PERSON>ly', notes: ['Cinnamon', 'Pepper', 'Osmanthus', 'Tobacco', 'Vanilla'], type: 'Oriental Spicy' },
        { name: 'Pegasus', brand: 'Parfums de Marly', notes: ['Bergamot', 'Heliotrope', 'Bitter Almond', 'Vanilla', 'Sandalwood'], type: 'Oriental Gourmand' },
        { name: 'Galloway', brand: 'Parfums de Marly', notes: ['Elemi', 'Lavender', 'Geranium', 'Cashmere Wood', 'Ambergris'], type: 'Woody Aromatic' },
        { name: 'Godolphin', brand: 'Parfums de Marly', notes: ['Thyme', 'Saffron', 'Cypress', 'Rose', 'Cedarwood'], type: 'Woody Spicy' }
    ];

    // Function to open search modal
    function openSearchModal() {
        searchModal.classList.add('active');
        // Ensure suggestions are shown when opening
        showSuggestions();
        // Reset animations by removing and re-adding content
        setTimeout(() => {
            searchInput.focus();
            // Trigger animation reset for suggestion tags
            const suggestionTags = document.querySelectorAll('.suggestion-tag');
            suggestionTags.forEach((tag, index) => {
                tag.style.animation = 'none';
                tag.offsetHeight; // Trigger reflow
                tag.style.animation = `tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) ${0.8 + index * 0.1}s forwards`;
            });
        }, 100);
    }

    // Open search modal from navbar
    if (searchIcon) {
        searchIcon.addEventListener('click', function(e) {
            e.preventDefault();
            openSearchModal();
        });
    }

    // Open search modal from floating search icon
    if (floatingSearch) {
        floatingSearch.addEventListener('click', function(e) {
            e.preventDefault();
            openSearchModal();
        });
    }

    // Smooth close search modal function
    function closeSearchModal() {
        if (searchModal && searchModal.classList.contains('active')) {
            // Capture current dimensions to prevent jumping
            const modalContent = searchModal.querySelector('.search-modal-content');
            const searchResults = modalContent.querySelector('.search-results');

            // Get exact current dimensions
            const contentRect = modalContent.getBoundingClientRect();
            const resultsRect = searchResults.getBoundingClientRect();

            // Lock ALL dimensions during closing animation - prevent any size changes
            modalContent.style.width = contentRect.width + 'px';
            modalContent.style.height = contentRect.height + 'px';
            modalContent.style.minWidth = contentRect.width + 'px';
            modalContent.style.maxWidth = contentRect.width + 'px';
            modalContent.style.minHeight = contentRect.height + 'px';
            modalContent.style.maxHeight = contentRect.height + 'px';
            modalContent.style.boxSizing = 'border-box';
            modalContent.style.flexShrink = '0';
            modalContent.style.flexGrow = '0';

            // Lock search results dimensions
            searchResults.style.width = resultsRect.width + 'px';
            searchResults.style.height = resultsRect.height + 'px';
            searchResults.style.minWidth = resultsRect.width + 'px';
            searchResults.style.maxWidth = resultsRect.width + 'px';
            searchResults.style.minHeight = resultsRect.height + 'px';
            searchResults.style.maxHeight = resultsRect.height + 'px';
            searchResults.style.overflow = 'hidden';
            searchResults.style.flexShrink = '0';
            searchResults.style.flexGrow = '0';

            // Prevent any content changes during closing
            modalContent.style.overflow = 'hidden';

            // Force no transforms to prevent any scaling/movement
            modalContent.style.transform = 'none';
            modalContent.style.setProperty('transform', 'none', 'important');

            // Add closing class for smooth animation
            searchModal.classList.add('closing');
            searchModal.classList.remove('active');

            // Clean up after animation completes
            setTimeout(() => {
                searchModal.classList.remove('closing');
                // Reset all locked styles
                modalContent.style.width = '';
                modalContent.style.height = '';
                modalContent.style.minWidth = '';
                modalContent.style.maxWidth = '';
                modalContent.style.minHeight = '';
                modalContent.style.maxHeight = '';
                modalContent.style.boxSizing = '';
                modalContent.style.flexShrink = '';
                modalContent.style.flexGrow = '';
                modalContent.style.transform = '';
                modalContent.style.overflow = '';
                searchResults.style.width = '';
                searchResults.style.height = '';
                searchResults.style.minWidth = '';
                searchResults.style.maxWidth = '';
                searchResults.style.minHeight = '';
                searchResults.style.maxHeight = '';
                searchResults.style.flexShrink = '';
                searchResults.style.flexGrow = '';
                searchResults.style.overflow = '';
                searchInput.value = '';
                // Reset to suggestions when reopened
                showSuggestions();
            }, 1200); // Match the CSS transition duration
        }
    }

    // Close search modal
    if (searchClose) {
        searchClose.addEventListener('click', closeSearchModal);
    }

    // Close modal when clicking outside
    if (searchModal) {
        searchModal.addEventListener('click', function(e) {
            if (e.target === searchModal) {
                closeSearchModal();
            }
        });
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && searchModal && searchModal.classList.contains('active')) {
            closeSearchModal();
        }
    });

    // Search functionality with smooth transitions
    if (searchInput) {
        let searchTimeout;

        searchInput.addEventListener('input', function() {
            // Don't process input changes during closing animation
            if (searchModal && (searchModal.classList.contains('closing') || !searchModal.classList.contains('active'))) {
                return;
            }

            // Clear previous timeout
            clearTimeout(searchTimeout);

            const query = this.value.toLowerCase().trim();

            // Only process if modal is fully active
            if (!searchModal.classList.contains('active')) {
                return;
            }

            // Capture current height before transition
            const currentHeight = searchResults.offsetHeight;
            searchResults.style.minHeight = currentHeight + 'px';

            // Add transitioning class for smooth animation
            searchResults.classList.add('transitioning');

            searchTimeout = setTimeout(() => {
                // Triple-check we're not closing during the timeout
                if (searchModal && (searchModal.classList.contains('closing') || !searchModal.classList.contains('active'))) {
                    searchResults.classList.remove('transitioning');
                    searchResults.style.minHeight = '';
                    return;
                }

                if (query === '') {
                    showSuggestions();
                } else {
                    performSearch(query);
                }

                // Remove transitioning class and reset height after content is updated
                setTimeout(() => {
                    if (searchModal && searchModal.classList.contains('active')) {
                        searchResults.classList.remove('transitioning');
                        searchResults.style.minHeight = '';
                    }
                }, 50);
            }, 200);
        });
    }

    // Suggestion tag clicks
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('suggestion-tag')) {
            // Don't process clicks during closing animation or if modal not active
            if (searchModal && (searchModal.classList.contains('closing') || !searchModal.classList.contains('active'))) {
                return;
            }

            if (searchInput && searchModal.classList.contains('active')) {
                searchInput.value = e.target.textContent;
                performSearch(e.target.textContent.toLowerCase());
            }
        }
    });

    function showSuggestions() {
        // Don't update content during closing animation
        if (searchModal && (searchModal.classList.contains('closing') || !searchModal.classList.contains('active'))) {
            return;
        }

        if (searchResults) {
            searchResults.innerHTML = `
                <div class="search-suggestions">
                    <h3>Popular Searches</h3>
                    <div class="suggestion-tags">
                        <span class="suggestion-tag">Layton</span>
                        <span class="suggestion-tag">Parfums de Marly</span>
                        <span class="suggestion-tag">Woody</span>
                        <span class="suggestion-tag">Fresh</span>
                        <span class="suggestion-tag">Oriental</span>
                        <span class="suggestion-tag">Citrus</span>
                    </div>
                </div>
            `;

            // Trigger staggered animations for suggestion tags with delay for smooth transition
            setTimeout(() => {
                const suggestionTags = document.querySelectorAll('.suggestion-tag');
                suggestionTags.forEach((tag, index) => {
                    tag.style.animation = 'none';
                    tag.offsetHeight; // Trigger reflow
                    tag.style.animation = `tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) ${0.2 + index * 0.1}s forwards`;
                });
            }, 100);
        }
    }

    function performSearch(query) {
        // Don't update content during closing animation
        if (searchModal && (searchModal.classList.contains('closing') || !searchModal.classList.contains('active'))) {
            return;
        }

        const results = fragrances.filter(fragrance =>
            fragrance.name.toLowerCase().includes(query) ||
            fragrance.brand.toLowerCase().includes(query) ||
            fragrance.type.toLowerCase().includes(query) ||
            fragrance.notes.some(note => note.toLowerCase().includes(query))
        );

        if (searchResults) {
            if (results.length > 0) {
                let resultsHTML = '<div class="search-results-list"><h3>Search Results</h3>';
                results.forEach(fragrance => {
                    resultsHTML += `
                        <div class="search-result-item">
                            <div class="result-main">
                                <h4>${fragrance.name}</h4>
                                <p class="result-brand">${fragrance.brand}</p>
                                <p class="result-type">${fragrance.type}</p>
                            </div>
                            <div class="result-notes">
                                <span class="notes-label">Notes:</span>
                                ${fragrance.notes.map(note => `<span class="note-tag">${note}</span>`).join('')}
                            </div>
                        </div>
                    `;
                });
                resultsHTML += '</div>';
                searchResults.innerHTML = resultsHTML;

                // Trigger staggered animations for search results with delay for smooth transition
                setTimeout(() => {
                    const resultItems = document.querySelectorAll('.search-result-item');
                    resultItems.forEach((item, index) => {
                        item.style.animation = 'none';
                        item.offsetHeight; // Trigger reflow
                        item.style.animation = `resultFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) ${0.1 + index * 0.1}s forwards`;
                    });
                }, 100);
            } else {
                searchResults.innerHTML = `
                    <div class="no-results">
                        <h3>No results found</h3>
                        <p>Try searching for different keywords or browse our popular searches above.</p>
                    </div>
                `;
            }
        }
    }

    // Initialize with suggestions
    showSuggestions();

    // Function to interpolate between two colors
    function interpolateColor(color1, color2, factor) {
        const rgb1 = hexToRgb(color1);
        const rgb2 = hexToRgb(color2);

        const r = Math.round(rgb1.r + factor * (rgb2.r - rgb1.r));
        const g = Math.round(rgb1.g + factor * (rgb2.g - rgb1.g));
        const b = Math.round(rgb1.b + factor * (rgb2.b - rgb1.b));

        return `rgb(${r}, ${g}, ${b})`;
    }

    // Function to convert hex to RGB
    function hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Function to update colors and vignette based on scroll position
    function updateColors() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        const windowHeight = window.innerHeight;

        // Calculate when user has scrolled past the video section (100vh)
        const videoSectionHeight = windowHeight; // 100vh
        const contentHeight = document.querySelector('.content')?.offsetHeight || windowHeight * 3;

        // Define transition zones - black stays much longer
        const blackDuration = contentHeight * 0.92; // Black stays for 92% of content height (was 85%)
        const transitionStart = videoSectionHeight + blackDuration; // Start transition much later
        const transitionRange = contentHeight * 0.08; // Use 8% of content height for transition (was 15%)
        const transitionEnd = transitionStart + transitionRange;

        // Keep background black until past video, then transition to white
        let backgroundColor;
        if (scrollTop < transitionStart) {
            // Still in or before video section - stay black
            backgroundColor = '#000000';
        } else if (scrollTop < transitionEnd) {
            // In transition zone - fade from black to white with slower easing
            const rawProgress = (scrollTop - transitionStart) / transitionRange;
            // Apply slower easing curve for smoother, more gradual fade
            const easedProgress = Math.pow(rawProgress, 0.7); // Slower than linear
            backgroundColor = interpolateColor('#000000', '#ffffff', easedProgress);
        } else {
            // Past transition zone - fully white
            backgroundColor = '#ffffff';
        }

        // Apply the color
        body.style.backgroundColor = backgroundColor;

        // Progressive text color transition based on background
        let textColor;
        if (scrollTop < transitionStart) {
            // Background is black - keep text white
            textColor = '#ffffff';
        } else if (scrollTop < transitionEnd) {
            // In transition zone - fade text from white to black
            const rawProgress = (scrollTop - transitionStart) / transitionRange;
            const easedProgress = Math.pow(rawProgress, 0.7); // Same easing as background
            textColor = interpolateColor('#ffffff', '#000000', easedProgress);
        } else {
            // Background is white - text should be black
            textColor = '#000000';
        }

        // Smooth fade-out for perfume rating section before background transition
        const perfumeRatingSection = document.querySelector('.perfume-rating');
        if (perfumeRatingSection) {
            // Start fading out much later - only 50px before background transition begins
            const ratingFadeStart = transitionStart - 50;
            const ratingFadeRange = 400; // Fade over 400px for gentle transition
            const ratingFadeEnd = ratingFadeStart + ratingFadeRange;

            if (scrollTop < ratingFadeStart) {
                // Before fade - fully visible (don't interfere with parallax)
                if (!perfumeRatingSection.classList.contains('parallax-active')) {
                    perfumeRatingSection.style.opacity = '1';
                    perfumeRatingSection.style.transition = 'opacity 0.3s ease';
                }
            } else if (scrollTop < ratingFadeEnd) {
                // During fade-out - smooth cubic easing
                const rawProgress = (scrollTop - ratingFadeStart) / ratingFadeRange;

                // Smooth cubic ease-out for natural fade
                const easedProgress = 1 - Math.pow(1 - rawProgress, 3);

                const opacity = 1 - easedProgress;
                perfumeRatingSection.style.opacity = Math.max(opacity, 0);
                perfumeRatingSection.style.transition = 'opacity 0.25s ease-out';
            } else {
                // After fade-out - completely hidden
                perfumeRatingSection.style.opacity = '0';
                perfumeRatingSection.style.transition = 'opacity 0.25s ease-out';
            }
        }

        // Apply text color to all relevant elements
        const textElements = [
            '.perfume-rating',
            '.rating-title',
            '.perfume-description',
            '.perfume-description p',
            '.additional-ratings',
            '.category-title',
            '.rating-label',
            '.rating-count',
            '.no-vote',
            '.gender-labels',
            '.price-labels',
            '.indicator-label',
            '.mood-indicators .indicator-label',
            '.season-indicators .indicator-label'
        ];

        textElements.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                // Skip elements that should maintain their special colors
                if (!element.classList.contains('score') &&
                    !element.classList.contains('votes') &&
                    !element.tagName.toLowerCase() === 'strong' ||
                    element.closest('.perfume-description strong')) {
                    element.style.color = textColor;
                    element.style.transition = 'color 0.3s ease';
                }
            });
        });

        // Handle special colored elements separately
        const specialElements = document.querySelectorAll('.perfume-description strong');
        specialElements.forEach(element => {
            if (scrollTop < transitionStart) {
                element.style.color = '#ffd43b'; // Original gold color
            } else if (scrollTop < transitionEnd) {
                const rawProgress = (scrollTop - transitionStart) / transitionRange;
                const easedProgress = Math.pow(rawProgress, 0.7);
                element.style.color = interpolateColor('#ffd43b', '#d4a017', easedProgress); // Gold to darker gold
            } else {
                element.style.color = '#b8860b'; // Dark gold for white background
            }
            element.style.transition = 'color 0.3s ease';
        });

        // Handle score and votes elements
        const scoreElements = document.querySelectorAll('.rating-title .score');
        scoreElements.forEach(element => {
            if (scrollTop < transitionStart) {
                element.style.color = '#ffd43b'; // Original gold
            } else if (scrollTop < transitionEnd) {
                const rawProgress = (scrollTop - transitionStart) / transitionRange;
                const easedProgress = Math.pow(rawProgress, 0.7);
                element.style.color = interpolateColor('#ffd43b', '#d4a017', easedProgress);
            } else {
                element.style.color = '#b8860b'; // Dark gold
            }
            element.style.transition = 'color 0.3s ease';
        });

        const votesElements = document.querySelectorAll('.rating-title .votes');
        votesElements.forEach(element => {
            if (scrollTop < transitionStart) {
                element.style.color = '#74c0fc'; // Original blue
            } else if (scrollTop < transitionEnd) {
                const rawProgress = (scrollTop - transitionStart) / transitionRange;
                const easedProgress = Math.pow(rawProgress, 0.7);
                element.style.color = interpolateColor('#74c0fc', '#1c7ed6', easedProgress); // Blue to darker blue
            } else {
                element.style.color = '#1864ab'; // Dark blue for white background
            }
            element.style.transition = 'color 0.3s ease';
        });

        // Enhanced navbar fade effect with dynamic visual effects
        // Start fading immediately, completely gone by 6% scroll (much faster)
        const navbarFadeEnd = 0.06;
        const scrollProgress = Math.min(scrollTop / (documentHeight * navbarFadeEnd), 1);

        // Apply gentle easing for smooth fade-out
        const navbarOpacity = 1 - (scrollProgress * scrollProgress * (3 - 2 * scrollProgress)); // Smoothstep

        // Calculate dynamic visual effects based on scroll progress
        const navbarBlur = scrollProgress * 8; // Blur increases as navbar fades
        const scaleAmount = 1 - (scrollProgress * 0.05); // Slight scale down (95% at full fade)
        const backdropBlur = scrollProgress * 15; // Backdrop blur increases

        // Apply enhanced navbar effects
        if (navbar) {
            navbar.style.opacity = navbarOpacity;
            navbar.style.transform = `scale(${scaleAmount})`;
            navbar.style.filter = `blur(${navbarBlur}px)`;
            navbar.style.backdropFilter = `blur(${backdropBlur}px) saturate(${1 + scrollProgress * 0.5})`;
            navbar.style.transition = 'opacity 0.1s ease, transform 0.1s ease, filter 0.1s ease, backdrop-filter 0.1s ease';

            // Add subtle glow effect as it fades
            const glowIntensity = scrollProgress * 0.3;
            navbar.style.boxShadow = `0 0 ${glowIntensity * 30}px rgba(255, 255, 255, ${glowIntensity})`;

            // Add CSS classes for state management
            if (scrollProgress > 0.1 && scrollProgress < 0.9) {
                navbar.classList.add('fading');
                navbar.classList.remove('fade-complete');
            } else if (scrollProgress >= 0.9) {
                navbar.classList.add('fade-complete');
                navbar.classList.remove('fading');
            } else {
                navbar.classList.remove('fading', 'fade-complete');
            }
        }

        // Top vignette effect with blur (disappears faster and more gently)
        // Start fading immediately, completely gone by 25% scroll (faster)
        const topVignetteEnd = 0.25;
        let topVignetteProgress = Math.min(scrollProgress / topVignetteEnd, 1);

        // Apply gentle cubic easing for ultra-smooth fade-out
        const gentleEase = 1 - Math.pow(topVignetteProgress, 3); // Cubic ease-out for gentler transition

        // Calculate blur reduction (starts at 8px, reduces to 0px)
        const blurAmount = gentleEase * 8;

        // Bottom/edge vignette effect (appears on scroll)
        // Start vignette after 5% scroll, reach full intensity at 80% scroll
        const vignetteStart = 0.05;
        const vignetteEnd = 0.8;
        let vignetteProgress = 0;

        if (scrollProgress > vignetteStart) {
            vignetteProgress = Math.min((scrollProgress - vignetteStart) / (vignetteEnd - vignetteStart), 1);
        }

        // Apply advanced easing for more realistic vignette progression
        // Combine smoothstep with exponential easing for natural feel
        const smoothStep = vignetteProgress * vignetteProgress * (3 - 2 * vignetteProgress);
        const exponentialEase = 1 - Math.pow(1 - vignetteProgress, 2.5);
        const easedVignetteProgress = (smoothStep * 0.6) + (exponentialEase * 0.4);

        // Video blur effect (increases with scroll) - Applied to all videos
        // Start blurring immediately, reach maximum blur at 50% scroll for stronger effect
        const videoBlurEnd = 0.5;
        let videoBlurProgress = Math.min(scrollProgress / videoBlurEnd, 1);

        // Apply smooth easing for natural blur progression
        const videoBlurEased = videoBlurProgress * videoBlurProgress * (3 - 2 * videoBlurProgress); // Smoothstep

        // Calculate blur amount (0px to 25px for stronger effect)
        const videoBlurAmount = videoBlurEased * 25;

        // Apply blur to all videos
        const allVideos = [
            document.getElementById('background-video'),
            document.getElementById('background-video-2'),
            document.getElementById('background-video-3')
        ];

        allVideos.forEach(vid => {
            if (vid) {
                vid.style.filter = `blur(${videoBlurAmount}px)`;
                vid.style.transition = 'filter 0.1s ease, opacity 1s ease-in-out';
            }
        });

        // Dynamic bottom vignette effect (increases with scroll)
        // Start vignette immediately, reach full intensity at 40% scroll for faster effect
        const bottomVignetteEnd = 0.4;
        let bottomVignetteProgress = Math.min(scrollProgress / bottomVignetteEnd, 1);

        // Apply smooth easing for natural vignette progression
        const vignetteEase = bottomVignetteProgress * bottomVignetteProgress * (3 - 2 * bottomVignetteProgress); // Smoothstep
        const vignetteIntensity = vignetteEase * 1.2; // Max intensity of 1.2 (much stronger)

        // Bottom blur effect (increases with scroll for more pronounced bottom blur)
        const bottomBlurAmount = vignetteEase * 15; // Max 15px additional blur at bottom

        // Update vignette intensity and bottom blur
        document.documentElement.style.setProperty('--vignette-intensity', vignetteIntensity);
        document.documentElement.style.setProperty('--bottom-blur', bottomBlurAmount + 'px');

        // Update other vignette overlays with CSS custom properties
        document.documentElement.style.setProperty('--top-vignette-opacity', gentleEase);
        document.documentElement.style.setProperty('--top-vignette-blur', blurAmount + 'px');
        document.documentElement.style.setProperty('--vignette-opacity', easedVignetteProgress);

        ticking = false;
    }

    // Optimized scroll handler using requestAnimationFrame
    function onScroll() {
        if (!ticking) {
            requestAnimationFrame(updateColors);
            ticking = true;
        }
    }

    window.addEventListener('scroll', onScroll, { passive: true });

    // Initial color update
    updateColors();

    // Language changer functionality
    const languageSelector = document.querySelector('.language-selector');
    const currentLang = document.getElementById('current-lang');
    const languageDropdown = document.getElementById('language-dropdown');
    const langOptions = document.querySelectorAll('.lang-option');

    // Toggle dropdown
    currentLang.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        languageSelector.classList.toggle('active');
        languageDropdown.classList.toggle('active');
    });

    // Language option selection
    langOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const selectedLang = this.getAttribute('data-lang');
            const selectedCode = this.getAttribute('data-code');

            // Update current language display
            const selectedFlagSvg = this.querySelector('.flag svg').cloneNode(true);
            selectedFlagSvg.setAttribute('width', '20');
            selectedFlagSvg.setAttribute('height', '14');

            currentLang.querySelector('.flag').innerHTML = '';
            currentLang.querySelector('.flag').appendChild(selectedFlagSvg);
            currentLang.querySelector('.lang-code').textContent = selectedCode;
            
            // Update HTML lang attribute
            document.documentElement.setAttribute('lang', selectedLang);
            
            // Close dropdown
            languageSelector.classList.remove('active');
            languageDropdown.classList.remove('active');
            
            // You can add language switching logic here
            console.log('Language changed to:', selectedLang);
        });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!languageSelector.contains(e.target)) {
            languageSelector.classList.remove('active');
            languageDropdown.classList.remove('active');
        }
    });

    // Close dropdown on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            languageSelector.classList.remove('active');
            languageDropdown.classList.remove('active');
        }
    });

    // Multiple background videos cycling
    const videos = [
        document.getElementById('background-video'),
        document.getElementById('background-video-2'),
        document.getElementById('background-video-3')
    ];

    let currentVideoIndex = 0;
    let videoTransitionInterval;
    let isPlaying = true;

    // Video control elements
    const playPauseBtn = document.getElementById('play-pause-btn');
    const nextVideoBtn = document.getElementById('next-video-btn');
    const playIcon = playPauseBtn.querySelector('.play-icon');
    const pauseIcon = playPauseBtn.querySelector('.pause-icon');

    function switchToNextVideo() {
        if (videos.length <= 1) return;

        const currentVideo = videos[currentVideoIndex];
        const nextVideoIndex = (currentVideoIndex + 1) % videos.length;
        const nextVideo = videos[nextVideoIndex];

        // Fade out current video
        currentVideo.style.opacity = '0';

        // After fade out, switch videos and fade in
        setTimeout(() => {
            currentVideo.style.display = 'none';
            nextVideo.style.display = 'block';
            nextVideo.style.opacity = '0';

            // Force reflow
            nextVideo.offsetHeight;

            // Fade in next video
            nextVideo.style.opacity = '1';

            currentVideoIndex = nextVideoIndex;
        }, 1000); // Match CSS transition duration
    }

    function startVideoRotation() {
        // Switch video every 15 seconds
        videoTransitionInterval = setInterval(switchToNextVideo, 15000);
    }

    function stopVideoRotation() {
        if (videoTransitionInterval) {
            clearInterval(videoTransitionInterval);
        }
    }

    // Initialize video rotation
    if (videos.length > 1) {
        // Ensure all videos are loaded before starting rotation
        let loadedVideos = 0;
        videos.forEach(video => {
            if (video) {
                video.addEventListener('loadeddata', () => {
                    loadedVideos++;
                    if (loadedVideos === videos.length) {
                        startVideoRotation();
                    }
                });
            }
        });
    }

    // Video control functionality
    function togglePlayPause() {
        const currentVideo = videos[currentVideoIndex];
        if (!currentVideo) return;

        if (isPlaying) {
            // Pause video
            currentVideo.pause();
            playIcon.style.display = 'block';
            pauseIcon.style.display = 'none';
            stopVideoRotation();
            isPlaying = false;
        } else {
            // Play video
            currentVideo.play();
            playIcon.style.display = 'none';
            pauseIcon.style.display = 'block';
            startVideoRotation();
            isPlaying = true;
        }
    }

    function manualNextVideo() {
        // Stop auto rotation temporarily
        stopVideoRotation();

        // Switch to next video
        switchToNextVideo();

        // Restart auto rotation after 3 seconds if playing
        if (isPlaying) {
            setTimeout(() => {
                startVideoRotation();
            }, 3000);
        }
    }

    // Event listeners for video controls
    if (playPauseBtn) {
        playPauseBtn.addEventListener('click', togglePlayPause);
    }

    if (nextVideoBtn) {
        nextVideoBtn.addEventListener('click', manualNextVideo);
    }

    // Initialize play/pause button state
    if (isPlaying) {
        playIcon.style.display = 'none';
        pauseIcon.style.display = 'block';
    }

    // Update play/pause state when videos change
    function updatePlayPauseState() {
        const currentVideo = videos[currentVideoIndex];
        if (currentVideo) {
            if (currentVideo.paused) {
                playIcon.style.display = 'block';
                pauseIcon.style.display = 'none';
                isPlaying = false;
            } else {
                playIcon.style.display = 'none';
                pauseIcon.style.display = 'block';
                isPlaying = true;
            }
        }
    }

    // Listen for video events to sync button state
    videos.forEach(video => {
        if (video) {
            video.addEventListener('play', updatePlayPauseState);
            video.addEventListener('pause', updatePlayPauseState);
        }
    });

    // Back to Top Button Functionality
    const backToTopBtn = document.getElementById('backToTop');
    const progressRing = document.querySelector('.progress-ring-progress');
    const circumference = 2 * Math.PI * 26; // radius = 26

    if (backToTopBtn && progressRing) {
        // Set up progress ring
        progressRing.style.strokeDasharray = circumference;
        progressRing.style.strokeDashoffset = circumference;

        function updateBackToTop() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
            const scrollPercent = scrollTop / documentHeight;

            // Show/hide button based on scroll position
            if (scrollTop > 300) {
                backToTopBtn.classList.add('visible');
            } else {
                backToTopBtn.classList.remove('visible');
            }

            // Update progress ring
            const offset = circumference - (scrollPercent * circumference);
            progressRing.style.strokeDashoffset = offset;

            // Add pulse effect when near bottom
            if (scrollPercent > 0.9) {
                backToTopBtn.classList.add('pulse');
            } else {
                backToTopBtn.classList.remove('pulse');
            }
        }

        // Floating search icon visibility and styling function
        function updateFloatingSearch() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const contentHeight = document.querySelector('.content')?.offsetHeight || window.innerHeight * 3;
            const windowHeight = window.innerHeight;

            // Calculate background transition points (same as in updateColors)
            const blackDuration = contentHeight * 0.92;
            const transitionStart = windowHeight + blackDuration;
            const transitionRange = contentHeight * 0.08;
            const transitionEnd = transitionStart + transitionRange;

            // Show floating search after scrolling past navbar fade (150px for smooth transition)
            if (scrollTop > 150) {
                floatingSearch.classList.add('visible');

                // Update styling based on background color
                if (scrollTop < transitionStart) {
                    // Black background - use light styling
                    floatingSearch.classList.add('dark-bg');
                    floatingSearch.classList.remove('white-bg');
                } else if (scrollTop >= transitionEnd) {
                    // White background - use dark styling
                    floatingSearch.classList.add('white-bg');
                    floatingSearch.classList.remove('dark-bg');
                } else {
                    // In transition - determine which style based on progress
                    const progress = (scrollTop - transitionStart) / transitionRange;
                    if (progress < 0.5) {
                        floatingSearch.classList.add('dark-bg');
                        floatingSearch.classList.remove('white-bg');
                    } else {
                        floatingSearch.classList.add('white-bg');
                        floatingSearch.classList.remove('dark-bg');
                    }
                }
            } else {
                floatingSearch.classList.remove('visible');
                // Default to dark-bg when hidden
                floatingSearch.classList.add('dark-bg');
                floatingSearch.classList.remove('white-bg');
            }
        }

        // Add back to top and floating search update to scroll handler
        const originalOnScroll = onScroll;
        onScroll = function() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateColors();
                    updateBackToTop();
                    if (floatingSearch) {
                        updateFloatingSearch();
                    }
                });
                ticking = true;
            }
        };

        // Update scroll event listener
        window.removeEventListener('scroll', originalOnScroll);
        window.addEventListener('scroll', onScroll, { passive: true });

        // Enhanced smooth scroll to top with custom easing
        backToTopBtn.addEventListener('click', function() {
            const startPosition = window.pageYOffset;
            const startTime = performance.now();
            const duration = 2000; // 2 seconds for very smooth transition

            function easeInOutCubic(t) {
                return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
            }

            function animateScroll(currentTime) {
                const timeElapsed = currentTime - startTime;
                const progress = Math.min(timeElapsed / duration, 1);
                const easedProgress = easeInOutCubic(progress);

                const currentPosition = startPosition * (1 - easedProgress);
                window.scrollTo(0, currentPosition);

                if (progress < 1) {
                    requestAnimationFrame(animateScroll);
                }
            }

            requestAnimationFrame(animateScroll);
        });

        // Initial calls
        updateBackToTop();
        if (floatingSearch) {
            updateFloatingSearch();
        }
    }

    // Parallax Elements
    const brandImage = document.querySelector('.brand-image');
    const laytonImage = document.querySelector('.layton-image');
    const laytonNotes = document.querySelector('.layton-notes');
    const productTitle = document.querySelector('.product-title');
    const fragranceNotes = document.querySelector('.fragrance-notes');
    const perfumeRating = document.querySelector('.perfume-rating');

    // Brand Image Parallax Effect (First in sequence)
    if (brandImage) {
        function updateBrandImageParallax() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const heroSection = document.querySelector('.hero');
            const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;

            // Start parallax effect much later in scroll sequence (60% of hero height)
            const triggerPoint = heroHeight * 0.6; // Start at 60% of hero height (much later)
            const parallaxRange = heroHeight * 0.8; // Effect lasts for 80% of hero height
            const fadeOutStart = heroHeight * 1.4; // Start fading out at 140% (much later)
            const fadeOutEnd = heroHeight * 1.8; // Complete fade out at 180% (much later)

            if (scrollTop > triggerPoint && scrollTop < fadeOutEnd) {
                brandImage.classList.add('parallax-active');

                let opacity, translateY;

                if (scrollTop < fadeOutStart) {
                    // Fade in and parallax phase
                    const progress = Math.min((scrollTop - triggerPoint) / parallaxRange, 1);

                    // Smooth easing function for natural movement (same as other elements)
                    const easeOutCubic = 1 - Math.pow(1 - progress, 3);

                    // Vertical parallax movement (slower than scroll speed)
                    translateY = -30 + (easeOutCubic * 20); // Move from -30px to -10px (subtle upward drift)
                    opacity = Math.min(easeOutCubic * 1.2, 1); // Fade in slightly faster than movement
                } else {
                    // Fade out phase
                    const fadeProgress = (scrollTop - fadeOutStart) / (fadeOutEnd - fadeOutStart);
                    const easedFadeProgress = Math.pow(fadeProgress, 2); // Faster fade out

                    translateY = -10; // Keep final position
                    opacity = 0.9 * (1 - easedFadeProgress); // Fade from 0.9 to 0
                }

                brandImage.style.transform = `translateY(${translateY}px)`;
                brandImage.style.opacity = opacity;
            } else if (scrollTop <= triggerPoint) {
                // Reset to hidden state when above trigger point
                brandImage.classList.remove('parallax-active');
                brandImage.style.transform = 'translateY(-30px)';
                brandImage.style.opacity = '0';
            } else {
                // Completely hidden when past fade out point
                brandImage.classList.remove('parallax-active');
                brandImage.style.transform = 'translateY(-10px)';
                brandImage.style.opacity = '0';
            }
        }
    }

    if (laytonImage) {
        function updateLaytonParallax() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const heroSection = document.querySelector('.hero');
            const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;

            // Start parallax effect after scrolling past the hero section
            const triggerPoint = heroHeight * 1.2; // Start at 120% of hero height (earlier)
            const parallaxRange = 400; // Distance over which the effect occurs

            if (scrollTop > triggerPoint) {
                laytonImage.classList.add('parallax-active');

                // Calculate progress (0 to 1) over the parallax range
                const progress = Math.min((scrollTop - triggerPoint) / parallaxRange, 1);

                // Smooth easing function for natural movement
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);

                // Apply parallax transformations with synchronized zoom
                const translateX = -100 + (easeOutCubic * 100); // Slide in from left
                const scale = 0.8 + (easeOutCubic * 0.2); // Zoom from 0.8 to 1.0
                const opacity = easeOutCubic; // Fade in

                laytonImage.style.transform = `translateX(${translateX}px) scale(${scale})`;
                laytonImage.style.opacity = opacity;
            } else {
                // Reset to hidden state when above trigger point
                laytonImage.classList.remove('parallax-active');
                laytonImage.style.transform = 'translateX(-100px) scale(0.8)';
                laytonImage.style.opacity = '0';
            }
        }
    }

    // Layton Notes Parallax Effect
    if (laytonNotes) {
        function updateLaytonNotesParallax() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const heroSection = document.querySelector('.hero');
            const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;

            // Start parallax effect much later than other elements
            const triggerPoint = heroHeight * 1.2; // Start at 120% of hero height (earlier)
            const parallaxRange = 400; // Same distance as bottle image

            if (scrollTop > triggerPoint) {
                laytonNotes.classList.add('parallax-active');

                // Calculate progress (0 to 1) over the parallax range
                const progress = Math.min((scrollTop - triggerPoint) / parallaxRange, 1);

                // Smooth easing function for natural movement
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);

                // Apply parallax transformations with synchronized zoom (slide from right)
                const translateX = 100 - (easeOutCubic * 100); // Slide in from right
                const scale = 0.8 + (easeOutCubic * 0.2); // Zoom from 0.8 to 1.0
                const opacity = easeOutCubic; // Fade in

                laytonNotes.style.transform = `translateX(${translateX}px) scale(${scale})`;
                laytonNotes.style.opacity = opacity;
            } else {
                // Reset to hidden state when above trigger point
                laytonNotes.classList.remove('parallax-active');
                laytonNotes.style.transform = 'translateX(100px) scale(0.8)';
                laytonNotes.style.opacity = '0';
            }
        }
    }

    // Product Title Parallax Effect
    if (productTitle) {
        function updateProductTitleParallax() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const heroSection = document.querySelector('.hero');
            const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;

            // Start parallax effect much later than other elements
            const triggerPoint = heroHeight * 1.2; // Start at 120% of hero height (earlier)
            const parallaxRange = 350; // Smooth transition range

            if (scrollTop > triggerPoint) {
                productTitle.classList.add('parallax-active');

                // Calculate progress (0 to 1) over the parallax range
                const progress = Math.min((scrollTop - triggerPoint) / parallaxRange, 1);

                // Smooth easing function for natural movement
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);

                // Apply parallax transformations (fade up and scale in)
                const translateY = 30 - (easeOutCubic * 30); // Slide up from below
                const scale = 0.9 + (easeOutCubic * 0.1); // Scale from 0.9 to 1.0
                const opacity = easeOutCubic; // Fade in

                productTitle.style.transform = `translateY(${translateY}px) scale(${scale})`;
                productTitle.style.opacity = opacity;
            } else {
                // Reset to hidden state when above trigger point
                productTitle.classList.remove('parallax-active');
                productTitle.style.transform = 'translateY(30px) scale(0.9)';
                productTitle.style.opacity = '0';
            }
        }
    }

    // Fragrance Notes Parallax Effect
    if (fragranceNotes) {
        function updateFragranceNotesParallax() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const heroSection = document.querySelector('.hero');
            const heroHeight = heroSection ? heroSection.offsetHeight : window.innerHeight;

            // Start parallax effect much later than other elements
            const triggerPoint = heroHeight * 1.2; // Start at 120% of hero height (earlier)
            const parallaxRange = 400; // Same distance as other images

            if (scrollTop > triggerPoint) {
                fragranceNotes.classList.add('parallax-active');

                // Calculate progress (0 to 1) over the parallax range
                const progress = Math.min((scrollTop - triggerPoint) / parallaxRange, 1);

                // Smooth easing function for natural movement
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);

                // Apply parallax transformations (slide from right, further than notes)
                const translateX = 150 - (easeOutCubic * 150); // Slide in from further right
                const scale = 0.8 + (easeOutCubic * 0.2); // Zoom from 0.8 to 1.0
                const opacity = easeOutCubic; // Fade in

                fragranceNotes.style.transform = `translateX(${translateX}px) scale(${scale})`;
                fragranceNotes.style.opacity = opacity;
            } else {
                // Reset to hidden state when above trigger point
                fragranceNotes.classList.remove('parallax-active');
                fragranceNotes.style.transform = 'translateX(150px) scale(0.8)';
                fragranceNotes.style.opacity = '0';
            }
        }
    }

    // Perfume Rating Parallax Effect
    if (perfumeRating) {
        function updatePerfumeRatingParallax() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const heroSection = document.querySelector('.hero');
            const heroHeight = heroSection ? heroHeight.offsetHeight : window.innerHeight;

            // Start parallax effect much later than other elements
            const triggerPoint = heroHeight * 1.8; // Start at 180% of hero height (extremely delayed)
            const parallaxRange = 300; // Shorter range for final element

            if (scrollTop > triggerPoint) {
                perfumeRating.classList.add('parallax-active');

                // Calculate progress (0 to 1) over the parallax range
                const progress = Math.min((scrollTop - triggerPoint) / parallaxRange, 1);

                // Smooth easing function for natural movement
                const easeOutCubic = 1 - Math.pow(1 - progress, 3);

                // Apply parallax transformations (fade up from bottom)
                const translateY = 50 - (easeOutCubic * 50); // Slide up from bottom

                // Only set opacity if fade-out hasn't taken control
                const currentOpacity = parseFloat(perfumeRating.style.opacity) || 1;
                if (currentOpacity > 0) {
                    const parallaxOpacity = easeOutCubic;
                    // Use the minimum of parallax opacity and current opacity (for fade-out)
                    perfumeRating.style.opacity = Math.min(parallaxOpacity, currentOpacity);
                }

                perfumeRating.style.transform = `translateY(${translateY}px)`;
            } else {
                // Reset to hidden state when above trigger point
                perfumeRating.classList.remove('parallax-active');
                perfumeRating.style.transform = 'translateY(50px)';
                // Only reset opacity if fade-out isn't controlling it
                if (parseFloat(perfumeRating.style.opacity) !== 0) {
                    perfumeRating.style.opacity = '0';
                }
            }
        }

        // Add parallax update to scroll handler
        const originalOnScrollWithParallax = onScroll;
        onScroll = function() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateColors();
                    if (backToTopBtn && progressRing) {
                        updateBackToTop();
                    }
                    if (floatingSearch) {
                        updateFloatingSearch();
                    }
                    if (brandImage) {
                        updateBrandImageParallax();
                    }
                    updateLaytonParallax();
                    if (laytonNotes) {
                        updateLaytonNotesParallax();
                    }
                    if (productTitle) {
                        updateProductTitleParallax();
                    }
                    if (fragranceNotes) {
                        updateFragranceNotesParallax();
                    }
                    if (perfumeRating) {
                        updatePerfumeRatingParallax();
                    }
                });
                ticking = true;
            }
        };

        // Update scroll event listener
        window.removeEventListener('scroll', originalOnScrollWithParallax);
        window.addEventListener('scroll', onScroll, { passive: true });

        // Initial parallax calls
        if (brandImage) {
            updateBrandImageParallax();
        }
        updateLaytonParallax();
        if (laytonNotes) {
            updateLaytonNotesParallax();
        }
        if (productTitle) {
            updateProductTitleParallax();
        }
        if (fragranceNotes) {
            updateFragranceNotesParallax();
        }
        if (perfumeRating) {
            updatePerfumeRatingParallax();
        }
    }
});