* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background-color: #000000;
    transition: background-color 0.3s ease;
    overflow-x: hidden;
    position: relative;
}

/* Navigation Bar */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: transparent;
    z-index: 1002;
    padding: 15px 0;
    transition: all 0.1s ease;
    will-change: opacity, transform, filter, backdrop-filter, box-shadow;
    transform-origin: center top;
}

/* Enhanced navbar fade states */
.navbar.fading {
    backdrop-filter: blur(10px) saturate(1.2);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

.navbar.fade-complete {
    pointer-events: none;
}

.nav-container {
    max-width: none;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0 20px;
    will-change: transform;
    transition: inherit;
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 30px;
    flex: 0.5;
}

.nav-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 0 0 auto;
    gap: 20px;
}

.nav-center {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 2;
    padding-right:130px;
    gap: 15px;
}

/* Brand Name Styling */
.brand-name {
    color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    margin: 0;
    cursor: pointer;
}

.brand-name:hover {
    color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
}

.brand-name::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.5));
    transition: width 0.3s ease;
}

.brand-name:hover::after {
    width: 100%;
}

/* Brand Search Icon */
.brand-search-icon {
    width: 18px;
    height: 18px;
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.brand-search-icon:hover {
    color: rgba(255, 255, 255, 0.7);
    transform: scale(1.1);
}

.brand-search-icon svg {
    width: 100%;
    height: 100%;
}

/* Search Modal with Enhanced Animations */
.search-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0);
    backdrop-filter: blur(0px);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    /* Ensure no scrollbars anywhere in modal */
    overflow: hidden;
}

.search-modal.active {
    opacity: 1;
    visibility: visible;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(15px);
}

/* Smooth closing animation */
.search-modal.closing {
    opacity: 0;
    visibility: visible;
    background: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    transition: all 1.2s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

.search-modal-content {
    background: linear-gradient(135deg, rgba(20, 20, 30, 0.95), rgba(40, 40, 60, 0.95));
    border-radius: 25px;
    padding: 40px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow:
        0 25px 80px rgba(0, 0, 0, 0.6),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transform: translateY(50px) scale(0.8);
    opacity: 0;
    transition: all 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    /* Fixed height to prevent content jumping */
    min-height: 400px;
}

.search-modal.active .search-modal-content {
    transform: translateY(0) scale(1);
    opacity: 1;
    animation: modalFloat 6s ease-in-out infinite 0.8s;
}

/* Smooth closing animation for modal content - ONLY opacity change */
.search-modal.closing .search-modal-content {
    opacity: 0;
    animation: none;
    transition: opacity 1.2s cubic-bezier(0.55, 0.085, 0.68, 0.53);
    /* NO transforms - maintain exact position and size */
    transform: none !important;
    overflow: hidden;
}

/* Modal glow effect */
.search-modal-content::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        rgba(100, 150, 255, 0.3),
        rgba(255, 100, 200, 0.2),
        rgba(100, 255, 150, 0.2),
        rgba(255, 200, 100, 0.3));
    border-radius: 27px;
    z-index: -1;
    opacity: 0;
    filter: blur(8px);
    transition: opacity 0.6s ease;
    animation: glowPulse 4s ease-in-out infinite;
}

.search-modal.active .search-modal-content::before {
    opacity: 0.6;
}

/* Smooth closing animation for glow effect */
.search-modal.closing .search-modal-content::before {
    opacity: 0;
    transition: opacity 1.2s cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

/* Prevent search results from changing during closing */
.search-modal.closing .search-results {
    overflow: hidden;
}

/* Disable transitions on search results during closing */
.search-modal.closing .search-results.transitioning {
    transition: none;
    transform: none;
    opacity: 1;
}

/* Keyframe Animations */
@keyframes modalBackdropEnter {
    0% {
        background: rgba(0, 0, 0, 0);
        backdrop-filter: blur(0px);
    }
    100% {
        background: rgba(0, 0, 0, 0.85);
        backdrop-filter: blur(15px);
    }
}

@keyframes modalEnter {
    0% {
        transform: translateY(50px) scale(0.8);
        opacity: 0;
    }
    60% {
        transform: translateY(-5px) scale(1.02);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes modalFloat {
    0%, 100% {
        transform: translateY(0) scale(1);
    }
    50% {
        transform: translateY(-3px) scale(1.001);
    }
}

@keyframes glowPulse {
    0%, 100% {
        opacity: 0.4;
        filter: blur(8px);
    }
    50% {
        opacity: 0.8;
        filter: blur(12px);
    }
}

@keyframes slideUpFade {
    0% {
        transform: translateY(25px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes smoothSlideUp {
    0% {
        transform: translateY(15px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes tagStagger {
    0% {
        transform: translateY(15px) scale(0.9);
        opacity: 0;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes resultFadeIn {
    0% {
        transform: translateY(10px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    transform: translateY(20px);
    opacity: 0;
    animation: slideUpFade 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s forwards;
}

.search-header h2 {
    color: #fff;
    font-size: 24px;
    font-weight: 300;
    margin: 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.search-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 30px;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: rotate(0deg) scale(1);
    opacity: 0;
    animation: slideUpFade 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s forwards;
}

.search-close:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
    transform: rotate(90deg) scale(1.15);
    box-shadow: 0 5px 20px rgba(255, 255, 255, 0.1);
}

.search-input-container {
    position: relative;
    margin-bottom: 30px;
    transform: translateY(20px);
    opacity: 0;
    animation: slideUpFade 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.4s forwards;
}

#searchInput {
    width: 100%;
    padding: 18px 55px 18px 25px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    color: #fff;
    font-size: 16px;
    outline: none;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(10px);
    position: relative;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

#searchInput::placeholder {
    color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

#searchInput:focus {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(100, 150, 255, 0.6);
    box-shadow:
        0 0 0 4px rgba(100, 150, 255, 0.1),
        0 8px 32px rgba(100, 150, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

#searchInput:focus::placeholder {
    color: rgba(255, 255, 255, 0.3);
    transform: translateX(5px);
}

.search-icon-input {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    color: rgba(255, 255, 255, 0.5);
}

.search-icon-input svg {
    width: 100%;
    height: 100%;
}

.search-suggestions {
    transform: translateY(20px);
    opacity: 0;
    animation: slideUpFade 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.6s forwards;
}

.search-suggestions h3 {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 300;
    margin: 0 0 20px 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.suggestion-tag {
    background: rgba(255, 255, 255, 0.08);
    color: rgba(255, 255, 255, 0.8);
    padding: 10px 18px;
    border-radius: 25px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    transform: translateY(15px) scale(0.9);
    opacity: 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Staggered animation for suggestion tags */
.suggestion-tag:nth-child(1) { animation: tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.8s forwards; }
.suggestion-tag:nth-child(2) { animation: tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.9s forwards; }
.suggestion-tag:nth-child(3) { animation: tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 1.0s forwards; }
.suggestion-tag:nth-child(4) { animation: tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 1.1s forwards; }
.suggestion-tag:nth-child(5) { animation: tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 1.2s forwards; }
.suggestion-tag:nth-child(6) { animation: tagStagger 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 1.3s forwards; }

.suggestion-tag:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.2),
        0 0 0 2px rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
}

/* Search Results with Enhanced Animations */
.search-results {
    transition: all 0.4s cubic-bezier(0.4, 0.0, 0.2, 1);
    transform: translateY(0);
    opacity: 1;
    min-height: 250px;
    /* Prevent height changes during transitions */
    overflow-y: auto;
    overflow-x: hidden;
    /* Hide scrollbar while keeping scroll functionality - STRONG RULES */
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers (Chrome, Safari, Edge) - STRONG RULES */
.search-results::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
}

/* Extra protection during transitions */
.search-results.transitioning {
    overflow: hidden !important;
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.search-results.transitioning::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
}

/* Universal scrollbar hiding for entire search modal */
.search-modal * {
    scrollbar-width: none !important;
    -ms-overflow-style: none !important;
}

.search-modal *::-webkit-scrollbar {
    display: none !important;
    width: 0 !important;
    height: 0 !important;
}

/* Reddit Review Section */
.reddit-review-section {
    margin: 40px 0;
    padding: 0 20px;
}

.reddit-review-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.reddit-review-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #FF4500, #FF6B35, #FF8E53);
    border-radius: 16px 16px 0 0;
}

.reddit-review-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(255, 69, 0, 0.2);
    border-color: rgba(255, 69, 0, 0.3);
}

.reddit-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.reddit-logo {
    display: flex;
    align-items: center;
    margin-right: 12px;
}

.reddit-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.reddit-platform {
    color: #FF4500;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.reddit-subreddit {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.reddit-votes {
    display: flex;
    align-items: center;
    background: rgba(255, 69, 0, 0.1);
    padding: 8px 12px;
    border-radius: 20px;
    border: 1px solid rgba(255, 69, 0, 0.3);
}

.upvote-icon {
    color: #FF4500;
    font-size: 14px;
    margin-right: 6px;
    font-weight: bold;
}

.vote-count {
    color: #FF4500;
    font-weight: 600;
    font-size: 14px;
}

.reddit-content {
    color: white;
}

.reddit-user {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    border: 2px solid rgba(255, 69, 0, 0.3);
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.username {
    color: #FF6B35;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 2px;
}

.post-time {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
}

.reddit-text {
    margin-bottom: 20px;
    line-height: 1.6;
}

.reddit-text p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 15px;
    margin: 0;
    font-style: italic;
}

.reddit-engagement {
    display: flex;
    gap: 24px;
    padding-top: 16px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.engagement-item {
    display: flex;
    align-items: center;
    gap: 6px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 13px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.engagement-item:hover {
    color: #FF6B35;
}

.engagement-icon {
    font-size: 14px;
}

.engagement-count,
.engagement-text {
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .reddit-review-section {
        margin: 30px 0;
        padding: 0 15px;
    }

    .reddit-review-card {
        padding: 20px;
    }

    .reddit-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .reddit-votes {
        align-self: flex-end;
    }

    .reddit-engagement {
        flex-wrap: wrap;
        gap: 16px;
    }
}

.search-results.transitioning {
    transform: translateY(-8px);
    opacity: 0.3;
}

.search-results-list {
    animation: slideUpFade 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.search-results-list h3 {
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 300;
    margin: 0 0 25px 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.search-result-item {
    background: rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 18px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    backdrop-filter: blur(10px);
    transform: translateY(10px);
    opacity: 0;
    animation: resultFadeIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* Staggered animation for search results */
.search-result-item:nth-child(2) { animation-delay: 0.1s; }
.search-result-item:nth-child(3) { animation-delay: 0.2s; }
.search-result-item:nth-child(4) { animation-delay: 0.3s; }
.search-result-item:nth-child(5) { animation-delay: 0.4s; }
.search-result-item:nth-child(6) { animation-delay: 0.5s; }

.search-result-item:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.2),
        0 0 0 2px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.result-main h4 {
    color: #fff;
    font-size: 18px;
    font-weight: 400;
    margin: 0 0 5px 0;
}

.result-brand {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    margin: 0 0 5px 0;
    font-weight: 300;
}

.result-type {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin: 0 0 15px 0;
    font-style: italic;
}

.result-notes {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
}

.notes-label {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
    margin-right: 5px;
}

.note-tag {
    background: rgba(255, 255, 255, 0.08);
    color: rgba(255, 255, 255, 0.8);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 11px;
    border: 1px solid rgba(255, 255, 255, 0.12);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    backdrop-filter: blur(5px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.note-tag:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #fff;
    transform: translateY(-1px) scale(1.05);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.no-results {
    text-align: center;
    padding: 50px 20px;
    animation: slideUpFade 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.no-results h3 {
    color: rgba(255, 255, 255, 0.8);
    font-size: 20px;
    font-weight: 300;
    margin: 0 0 15px 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.no-results p {
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
    margin: 0;
    line-height: 1.6;
}

/* Advanced Visual Effects */
.search-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(100, 150, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(255, 100, 200, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(100, 255, 150, 0.1) 0%, transparent 50%);
    opacity: 0;
    transition: opacity 0.8s ease;
    pointer-events: none;
}

.search-modal.active::before {
    opacity: 1;
    animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
    0%, 100% {
        transform: translateX(0) translateY(0) scale(1);
    }
    25% {
        transform: translateX(-10px) translateY(-5px) scale(1.02);
    }
    50% {
        transform: translateX(5px) translateY(10px) scale(0.98);
    }
    75% {
        transform: translateX(8px) translateY(-8px) scale(1.01);
    }
}

/* Hide scrollbar for modal content */
.search-modal-content {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.search-modal-content::-webkit-scrollbar {
    display: none; /* WebKit */
}

/* Responsive Search Modal */
@media (max-width: 768px) {
    .floating-search {
        width: 45px;
        height: 45px;
        top: 20px;
        right: 20px;
    }

    .floating-search-icon {
        width: 18px;
        height: 18px;
    }

    .search-modal-content {
        padding: 25px 20px;
        margin: 15px;
        width: calc(100% - 30px);
        border-radius: 20px;
        animation: modalFloat 8s ease-in-out infinite;
    }

    .search-header h2 {
        font-size: 18px;
    }

    .search-close {
        width: 35px;
        height: 35px;
        font-size: 24px;
    }

    #searchInput {
        padding: 15px 45px 15px 18px;
        font-size: 14px;
        border-radius: 18px;
    }

    .search-result-item {
        padding: 18px;
        border-radius: 18px;
    }

    .result-main h4 {
        font-size: 16px;
    }

    .suggestion-tag {
        padding: 8px 14px;
        font-size: 13px;
        border-radius: 20px;
    }

    .note-tag {
        padding: 4px 8px;
        font-size: 10px;
        border-radius: 12px;
    }
}

@media (max-width: 480px) {
    .search-modal-content {
        padding: 20px 15px;
        margin: 10px;
        width: calc(100% - 20px);
    }

    .search-header {
        margin-bottom: 20px;
    }

    .search-input-container {
        margin-bottom: 20px;
    }

    .suggestion-tags {
        gap: 8px;
    }

    .result-notes {
        gap: 6px;
        margin-top: 10px;
    }
}

.nav-link {
    color: rgba(255, 255, 255, 0.5);
    text-decoration: none;
    font-size: 16px;
    font-weight: 500;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: rgba(255, 255, 255, 0.7);
    transform: translateY(-2px);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.5));
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Header Image in Navigation */
#header-image {
    height: 40px;
    width: auto;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
    border-radius: 8px;
}

#header-image:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.4));
}

.search-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.search-container:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: scale(1.1);
    box-shadow: 0 5px 20px rgba(255, 255, 255, 0.1);
}

.search-icon {
    width: 16px;
    height: 16px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.search-container:hover .search-icon {
    color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

/* Language Changer */
.language-changer {
    position: relative;
    z-index: 1004;
    display: flex;
    align-items: center;
    margin-left: 20px;
}

.language-selector {
    position: relative;
    display: inline-block;
}

.current-lang {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(255, 255, 255, 0.08);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    font-size: 14px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
    min-width: 75px;
    justify-content: center;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Animated background gradient */
.current-lang::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.6s ease;
}

.current-lang:hover::before {
    left: 100%;
}

.current-lang:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.2),
        0 0 20px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.current-lang .flag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
    position: relative;
    z-index: 1;
}

.current-lang .flag svg {
    border-radius: 3px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.current-lang:hover .flag svg {
    transform: scale(1.1) rotate(2deg);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.current-lang .lang-code {
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 1px;
    text-transform: uppercase;
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 12px;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
    border: 2px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    overflow: hidden;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1003;
    min-width: 160px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.6),
        0 0 30px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Dropdown arrow */
.language-dropdown::before {
    content: '';
    position: absolute;
    top: -8px;
    right: 20px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid rgba(0, 0, 0, 0.95);
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.3));
}

.language-dropdown.active {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.lang-option {
    display: flex;
    align-items: center;
    gap: 14px;
    padding: 14px 18px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.lang-option:last-child {
    border-bottom: none;
}

/* Hover effect background */
.lang-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    transition: left 0.5s ease;
}

.lang-option:hover::before {
    left: 100%;
}

.lang-option:hover {
    background: rgba(255, 255, 255, 0.08);
    color: rgba(255, 255, 255, 1);
    transform: translateX(8px) scale(1.02);
    box-shadow: inset 4px 0 0 rgba(255, 255, 255, 0.3);
}

.lang-option .flag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.lang-option .flag svg {
    border-radius: 2px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.lang-option:hover .flag svg {
    transform: scale(1.1) rotate(2deg);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
}

.lang-option .lang-name {
    font-weight: 500;
    flex: 1;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.lang-option:hover .lang-name {
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.lang-option[data-lang="ar"] .lang-name {
    font-family: 'Arial', sans-serif;
    direction: rtl;
    text-align: right;
}

/* Arrow indicator for dropdown */
.current-lang::after {
    content: '▼';
    font-size: 10px;
    margin-left: 6px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    color: rgba(255, 255, 255, 0.6);
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.current-lang:hover::after {
    color: rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
}

.language-selector.active .current-lang::after {
    transform: rotate(180deg) scale(1.1);
    color: rgba(255, 255, 255, 0.9);
}

/* Staggered animation for dropdown items */
.lang-option:nth-child(1) { animation-delay: 0.1s; }
.lang-option:nth-child(2) { animation-delay: 0.15s; }
.lang-option:nth-child(3) { animation-delay: 0.2s; }

.language-dropdown.active .lang-option {
    animation: slideInFromRight 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsive navigation */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 20px;
    }
    
    .nav-left,
    .nav-right {
        gap: 20px;
    }
    
    .nav-link {
        font-size: 14px;
    }
    
    #header-image {
        height: 35px;
    }

    .search-container {
        width: 32px;
        height: 32px;
    }

    .search-icon {
        width: 14px;
        height: 14px;
    }
}

@media (max-width: 480px) {
    .nav-left,
    .nav-right {
        gap: 15px;
    }
    
    .nav-link {
        font-size: 13px;
    }

    #header-image {
        height: 30px;
    }

    .search-container {
        width: 28px;
        height: 28px;
    }

    .search-icon {
        width: 12px;
        height: 12px;
    }
}





.container {
    max-width: 100%;
    margin: 0 auto;
}

.hero {
    height: 100vh;
    position: relative;
    overflow: hidden;
}

/* Additional blur overlay for bottom-focused effect */
.hero::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50%;
    background: transparent;
    backdrop-filter: blur(var(--bottom-blur, 0px));
    z-index: 0;
    pointer-events: none;
    transition: backdrop-filter 0.1s ease;
    mask: linear-gradient(to top, black 0%, transparent 100%);
    -webkit-mask: linear-gradient(to top, black 0%, transparent 100%);
}

/* Brand Image Section */
.brand-image-section {
    position: relative;
    width: 100%;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    overflow: hidden;
}

.brand-image {
    max-width: 80%;
    max-height: 80%;
    object-fit: contain;
    opacity: 0;
    filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.5));
    transition: none;
    will-change: transform, opacity, filter;
    transform: translateY(-30px);
}



#background-video,
#background-video-2,
#background-video-3 {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
    transition: opacity 1s ease-in-out;
}

/* Dynamic scroll-responsive vignette overlay */
.hero::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(ellipse 150% 100% at 50% 100%,
            transparent 0%,
            transparent 20%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.6)) 50%,
            rgba(0, 0, 0, var(--vignette-intensity, 0)) 80%,
            rgba(0, 0, 0, var(--vignette-intensity, 0)) 100%
        ),
        linear-gradient(to top,
            rgba(0, 0, 0, var(--vignette-intensity, 0)) 0%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.9)) 15%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.7)) 30%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.5)) 45%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.3)) 60%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.1)) 80%,
            transparent 100%
        ),
        radial-gradient(ellipse 200% 150% at 50% 100%,
            transparent 0%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.4)) 60%,
            rgba(0, 0, 0, calc(var(--vignette-intensity, 0) * 0.8)) 100%
        );
    z-index: 1;
    pointer-events: none;
    transition: all 0.1s ease-out;
}

/* Video Controls */
.video-controls {
    position: absolute;
    bottom: 30px;
    right: 30px;
    display: flex;
    gap: 15px;
    z-index: 10;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
}

.hero:hover .video-controls {
    opacity: 1;
    transform: translateY(0);
    pointer-events: all;
}

.video-control-btn {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow:
        0 4px 15px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.video-control-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left 0.6s ease;
}

.video-control-btn:hover::before {
    left: 100%;
}

.video-control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-3px) scale(1.05);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 1);
}

.video-control-btn:active {
    transform: translateY(-1px) scale(1.02);
    transition: all 0.1s ease;
}

.video-control-btn svg {
    width: 20px;
    height: 20px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.video-control-btn:hover svg {
    transform: scale(1.1);
}

/* Responsive video controls */
@media (max-width: 768px) {
    .video-controls {
        bottom: 20px;
        right: 20px;
        gap: 12px;
    }

    .video-control-btn {
        width: 45px;
        height: 45px;
    }

    .video-control-btn svg {
        width: 18px;
        height: 18px;
    }
}

@media (max-width: 480px) {
    .video-controls {
        bottom: 15px;
        right: 15px;
        gap: 10px;
    }

    .video-control-btn {
        width: 40px;
        height: 40px;
    }

    .video-control-btn svg {
        width: 16px;
        height: 16px;
    }
}

/* Floating Search Icon */
.floating-search {
    position: fixed;
    top: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px) scale(0.8);
    transition: all 0.8s cubic-bezier(0.23, 1, 0.32, 1);
    backdrop-filter: blur(15px);
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.floating-search.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.floating-search:hover {
    transform: translateY(-2px) scale(1.05);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.12));
    border-color: rgba(255, 255, 255, 0.35);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.floating-search-icon {
    width: 20px;
    height: 20px;
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.floating-search:hover .floating-search-icon {
    color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

/* Floating Search - Dark Background State (default) */
.floating-search.dark-bg {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08));
    border: 1px solid rgba(255, 255, 255, 0.25);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.2),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.floating-search.dark-bg:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.12));
    border-color: rgba(255, 255, 255, 0.35);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.floating-search.dark-bg .floating-search-icon {
    color: rgba(255, 255, 255, 0.9);
}

.floating-search.dark-bg:hover .floating-search-icon {
    color: rgba(255, 255, 255, 1);
}

/* Floating Search - White Background State */
.floating-search.white-bg {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.08), rgba(0, 0, 0, 0.04));
    border: 1px solid rgba(0, 0, 0, 0.15);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 0 0 1px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.floating-search.white-bg:hover {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.12), rgba(0, 0, 0, 0.06));
    border-color: rgba(0, 0, 0, 0.25);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.floating-search.white-bg .floating-search-icon {
    color: rgba(0, 0, 0, 0.8);
}

.floating-search.white-bg:hover .floating-search-icon {
    color: rgba(0, 0, 0, 1);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 60px;
    height: 60px;
    background: rgba(0, 0, 0, 0.8);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px) scale(0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.back-to-top:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
}

.back-to-top:active {
    transform: translateY(0) scale(0.95);
}

.back-to-top-icon {
    width: 24px;
    height: 24px;
    color: rgba(255, 255, 255, 0.9);
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

.back-to-top:hover .back-to-top-icon {
    color: rgba(255, 255, 255, 1);
    transform: translateY(-2px);
}

.back-to-top-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.progress-ring {
    transform: rotate(-90deg);
    width: 100%;
    height: 100%;
}

.progress-ring-circle {
    transition: stroke-dasharray 0.3s ease;
}

.progress-ring-progress {
    stroke-dasharray: 163.36;
    stroke-dashoffset: 163.36;
    transition: stroke-dashoffset 0.3s ease;
}

@keyframes pulse {
    0%, 100% {
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    50% {
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.3),
            0 0 0 1px rgba(255, 255, 255, 0.1),
            0 0 0 10px rgba(255, 255, 255, 0);
    }
}

.back-to-top.pulse {
    animation: pulse 2s infinite;
}

/* Layton Perfume Image with Parallax Effect and Glow */
.layton-image {
    width: 300px;
    height: auto;
    margin: 210px 0 0 60px;
    display: inline-block;
    vertical-align: top;
    opacity: 0;
    transform: translateX(-100px) scale(0.8);
    transition: none;
    will-change: transform, opacity, filter;
    cursor: pointer;
    filter: brightness(1) contrast(1) saturate(1) drop-shadow(0 0 20px rgba(100, 150, 255, 0.3));
    transform-origin: center center;
    animation: perfumeGlow 4s ease-in-out infinite;
}

.layton-image.parallax-active {
    transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

/* Advanced Hover Effect */
.layton-image:hover {
    transform: translateX(0px) scale(1.15) translateY(-10px) !important;
    filter: brightness(1.3) contrast(1.2) saturate(1.4) drop-shadow(0 20px 40px rgba(0, 0, 0, 0.6));
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    z-index: 10;
    position: relative;
}

/* Smooth transition back to normal state */
.layton-image:not(:hover) {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

/* Perfume Glow Animation */
@keyframes perfumeGlow {
    0%, 100% {
        filter: brightness(1) contrast(1) saturate(1) drop-shadow(0 0 20px rgba(100, 150, 255, 0.3));
    }
    50% {
        filter: brightness(1.1) contrast(1.1) saturate(1.2) drop-shadow(0 0 30px rgba(100, 150, 255, 0.5));
    }
}

/* Product Title Section */
.product-title {
    display: inline-block;
    vertical-align: top;
    margin: 220px 0 0 0px;
    opacity: 0;
    transform: translateY(30px) scale(0.9);
    transition: none;
    will-change: transform, opacity;
    text-align: center;
}

.product-title.parallax-active {
    transition: transform 0.8s ease-out, opacity 0.8s ease-out;
}

/* Brand Name Styling */
.brand-name {
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    font-weight: 400;
    color: #ffffff;
    margin: 0 0 5px 0;
    letter-spacing: 4px;
    text-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
}

.brand-location {
    font-family: 'Playfair Display', serif;
    font-size: 14px;
    font-weight: 300;
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0 25px 0;
    letter-spacing: 3px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.product-name {
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    font-weight: 400;
    color: #ffffff;
    margin: 0 0 15px 0;
    letter-spacing: 3px;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    text-transform: uppercase;
}

/* Premium Price Container */
.product-price-container {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

/* Luxury Price Badge - Base Styling */
.price-badge {
    position: relative;
    display: inline-block;
    padding: 15px 25px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(20, 20, 20, 0.9));
    border: 2px solid #2ecc71;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    box-shadow:
        0 8px 25px rgba(46, 204, 113, 0.4),
        0 0 15px rgba(46, 204, 113, 0.3),
        inset 0 1px 0 rgba(46, 204, 113, 0.2);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

/* Price Badge - Green Theme */
.price-badge.price-green {
    border-color: #2ecc71;
    box-shadow:
        0 8px 25px rgba(46, 204, 113, 0.4),
        0 0 15px rgba(46, 204, 113, 0.3),
        inset 0 1px 0 rgba(46, 204, 113, 0.2);
}

/* Price Badge - Gold Theme */
.price-badge.price-gold {
    border-color: #d4af37;
    box-shadow:
        0 8px 25px rgba(212, 175, 55, 0.4),
        0 0 15px rgba(212, 175, 55, 0.3),
        inset 0 1px 0 rgba(212, 175, 55, 0.2);
}

/* Interactive Hover Effects - Green Theme */
.price-badge.price-green:hover {
    transform: translateY(-3px) scale(1.05);
    border-color: #27ae60;
    box-shadow:
        0 15px 40px rgba(46, 204, 113, 0.6),
        0 0 35px rgba(46, 204, 113, 0.5),
        inset 0 1px 0 rgba(46, 204, 113, 0.3),
        inset 0 0 20px rgba(46, 204, 113, 0.1);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(30, 30, 30, 0.95));
}

/* Interactive Hover Effects - Gold Theme */
.price-badge.price-gold:hover {
    transform: translateY(-3px) scale(1.05);
    border-color: #f4d03f;
    box-shadow:
        0 15px 40px rgba(212, 175, 55, 0.6),
        0 0 35px rgba(212, 175, 55, 0.5),
        inset 0 1px 0 rgba(212, 175, 55, 0.3),
        inset 0 0 20px rgba(212, 175, 55, 0.1);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(30, 30, 30, 0.95));
}

/* Floating Animation */
.price-badge {
    animation: priceFloat 4s ease-in-out infinite;
}

@keyframes priceFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

.price-badge:hover {
    animation: none;
}

/* Corner Ornaments - Base */
.price-ornament {
    position: absolute;
    width: 8px;
    height: 8px;
    border: 1px solid #2ecc71;
    transition: all 0.3s ease;
}

/* Price Ornaments - Green Theme */
.price-badge.price-green .price-ornament {
    border-color: #2ecc71;
}

/* Price Ornaments - Gold Theme */
.price-badge.price-gold .price-ornament {
    border-color: #d4af37;
}

.price-ornament.top-left {
    top: 5px;
    left: 5px;
    border-right: none;
    border-bottom: none;
}

.price-ornament.top-right {
    top: 5px;
    right: 5px;
    border-left: none;
    border-bottom: none;
}

.price-ornament.bottom-left {
    bottom: 5px;
    left: 5px;
    border-right: none;
    border-top: none;
}

.price-ornament.bottom-right {
    bottom: 5px;
    right: 5px;
    border-left: none;
    border-top: none;
}

.price-badge.price-green:hover .price-ornament {
    border-color: #27ae60;
    transform: scale(1.2);
}

.price-badge.price-gold:hover .price-ornament {
    border-color: #f4d03f;
    transform: scale(1.2);
}

/* Shimmer Effect */
.price-shimmer {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 3s infinite;
    pointer-events: none;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Price Text Styling - Base */
.product-price {
    font-family: 'Playfair Display', serif;
    font-weight: 700;
    color: #2ecc71;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(46, 204, 113, 0.5);
    position: relative;
    z-index: 2;
    display: flex;
    align-items: baseline;
    gap: 4px;
    transition: all 0.3s ease;
}

/* Price Text - Green Theme */
.price-badge.price-green .product-price {
    color: #2ecc71;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(46, 204, 113, 0.5);
}

/* Price Text - Gold Theme */
.price-badge.price-gold .product-price {
    color: #d4af37;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(212, 175, 55, 0.5);
}

.price-currency {
    font-size: 32px;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.price-unit {
    font-size: 18px;
    letter-spacing: 2px;
    text-transform: uppercase;
    opacity: 0.9;
    transition: all 0.3s ease;
}

.price-badge.price-green:hover .price-currency {
    font-size: 34px;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(46, 204, 113, 0.8);
}

.price-badge.price-green:hover .price-unit {
    opacity: 1;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 10px rgba(46, 204, 113, 0.6);
}

.price-badge.price-gold:hover .price-currency {
    font-size: 34px;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(212, 175, 55, 0.8);
}

.price-badge.price-gold:hover .price-unit {
    opacity: 1;
    text-shadow:
        0 2px 4px rgba(0, 0, 0, 0.4),
        0 0 10px rgba(212, 175, 55, 0.6);
}

/* Pulsing Glow Effect - Base */
.price-glow {
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.3) 0%, transparent 70%);
    border-radius: 20px;
    opacity: 0;
    animation: pulseGlow 2s ease-in-out infinite;
    pointer-events: none;
    transition: all 0.3s ease;
}

/* Price Glow - Green Theme */
.price-badge.price-green .price-glow {
    background: radial-gradient(circle, rgba(46, 204, 113, 0.3) 0%, transparent 70%);
}

/* Price Glow - Gold Theme */
.price-badge.price-gold .price-glow {
    background: radial-gradient(circle, rgba(212, 175, 55, 0.3) 0%, transparent 70%);
}

@keyframes pulseGlow {
    0%, 100% { opacity: 0; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

.price-badge:hover .price-glow {
    animation: none;
    opacity: 0.6;
}

/* Premium Subtitle - Red Theme with Intense Glow */
.price-subtitle {
    font-family: 'Playfair Display', serif;
    font-size: 12px;
    font-weight: 500;
    color: #dc3545;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-style: italic;
    text-shadow:
        0 1px 3px rgba(0, 0, 0, 0.5),
        0 0 10px rgba(220, 53, 69, 0.8),
        0 0 20px rgba(220, 53, 69, 0.6),
        0 0 30px rgba(220, 53, 69, 0.4),
        0 0 40px rgba(220, 53, 69, 0.2);
    transition: all 0.3s ease;
    animation: intensiveRedGlow 2.5s ease-in-out infinite;
}

@keyframes intensiveRedGlow {
    0%, 100% {
        text-shadow:
            0 1px 3px rgba(0, 0, 0, 0.5),
            0 0 10px rgba(220, 53, 69, 0.8),
            0 0 20px rgba(220, 53, 69, 0.6),
            0 0 30px rgba(220, 53, 69, 0.4),
            0 0 40px rgba(220, 53, 69, 0.2);
    }
    50% {
        text-shadow:
            0 1px 3px rgba(0, 0, 0, 0.6),
            0 0 15px rgba(220, 53, 69, 1),
            0 0 30px rgba(220, 53, 69, 0.8),
            0 0 45px rgba(220, 53, 69, 0.6),
            0 0 60px rgba(220, 53, 69, 0.4),
            0 0 75px rgba(220, 53, 69, 0.2);
    }
}

.product-price-container:hover .price-subtitle {
    color: #ff4757;
    text-shadow:
        0 1px 3px rgba(0, 0, 0, 0.6),
        0 0 20px rgba(220, 53, 69, 1),
        0 0 35px rgba(220, 53, 69, 0.9),
        0 0 50px rgba(220, 53, 69, 0.7),
        0 0 65px rgba(220, 53, 69, 0.5),
        0 0 80px rgba(220, 53, 69, 0.3);
    animation: none;
    transform: scale(1.05);
}

/* Advanced Quality Selector */
.quality-selector-container {
    margin-top: 25px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

/* Quality Header */
.quality-header {
    text-align: center;
    margin-bottom: 5px;
}

.quality-title {
    font-family: 'Playfair Display', serif;
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 5px 0;
    letter-spacing: 2px;
    text-transform: uppercase;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
}

.quality-subtitle {
    font-family: 'Playfair Display', serif;
    font-size: 11px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 1.5px;
    text-transform: uppercase;
    font-style: italic;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Quality Options Container */
.quality-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
    max-width: 280px;
}

/* Hide Default Radio Buttons */
.quality-option input[type="radio"] {
    display: none;
}

/* Quality Option Styling */
.quality-option {
    position: relative;
    cursor: pointer;
}

.quality-label {
    display: block;
    cursor: pointer;
}

/* Quality Badge */
.quality-badge {
    position: relative;
    display: flex;
    align-items: center;
    padding: 12px 18px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(15, 15, 15, 0.8));
    border: 1.5px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    box-shadow:
        0 4px 15px rgba(255, 255, 255, 0.1),
        0 0 10px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
}

/* Hover Effects */
.quality-badge:hover {
    transform: translateY(-2px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.6);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(20, 20, 20, 0.9));
    box-shadow:
        0 8px 25px rgba(255, 255, 255, 0.2),
        0 0 25px rgba(255, 255, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 0 15px rgba(255, 255, 255, 0.05);
}

/* Selected State - Default Golden */
.quality-option input[type="radio"]:checked + .quality-label .quality-badge {
    border-color: #d4af37;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(25, 25, 25, 0.95));
    box-shadow:
        0 6px 20px rgba(212, 175, 55, 0.4),
        0 0 30px rgba(212, 175, 55, 0.4),
        inset 0 1px 0 rgba(212, 175, 55, 0.3),
        inset 0 0 20px rgba(212, 175, 55, 0.1);
    transform: scale(1.03);
}

/* Identical Quality - Gold Effect (Override Green for Identical) */
.quality-option[data-quality="identical"] input[type="radio"]:checked + .quality-label .quality-badge {
    border-color: #d4af37 !important;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(25, 25, 25, 0.95)) !important;
    box-shadow:
        0 6px 20px rgba(212, 175, 55, 0.4),
        0 0 30px rgba(212, 175, 55, 0.4),
        inset 0 1px 0 rgba(212, 175, 55, 0.3),
        inset 0 0 20px rgba(212, 175, 55, 0.1) !important;
}

/* Top Quality - Green Effect */
.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .quality-badge {
    border-color: #2ecc71 !important;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9), rgba(25, 25, 25, 0.95)) !important;
    box-shadow:
        0 6px 20px rgba(46, 204, 113, 0.4),
        0 0 30px rgba(46, 204, 113, 0.4),
        inset 0 1px 0 rgba(46, 204, 113, 0.3),
        inset 0 0 20px rgba(46, 204, 113, 0.1) !important;
}

/* Quality Content */
.quality-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 3px;
    position: relative;
    z-index: 2;
}

.quality-name {
    font-family: 'Playfair Display', serif;
    font-size: 16px;
    font-weight: 600;
    color: #ffffff;
    letter-spacing: 1px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.quality-description {
    font-family: 'Playfair Display', serif;
    font-size: 11px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.7);
    letter-spacing: 0.5px;
    font-style: italic;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
}

/* Enhanced Text on Selection - Default Golden */
.quality-option input[type="radio"]:checked + .quality-label .quality-name {
    color: #f4d03f;
    text-shadow:
        0 1px 3px rgba(0, 0, 0, 0.4),
        0 0 8px rgba(212, 175, 55, 0.5);
}

.quality-option input[type="radio"]:checked + .quality-label .quality-description {
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Top Quality - Green Text Effects */
.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .quality-name {
    color: #2ecc71 !important;
    text-shadow:
        0 1px 3px rgba(0, 0, 0, 0.4),
        0 0 8px rgba(46, 204, 113, 0.5) !important;
}

.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .quality-description {
    color: rgba(255, 255, 255, 0.9) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Identical Quality - Gold Text Effects */
.quality-option[data-quality="identical"] input[type="radio"]:checked + .quality-label .quality-name {
    color: #d4af37 !important;
    text-shadow:
        0 1px 3px rgba(0, 0, 0, 0.4),
        0 0 8px rgba(212, 175, 55, 0.5) !important;
}

.quality-option[data-quality="identical"] input[type="radio"]:checked + .quality-label .quality-description {
    color: rgba(255, 255, 255, 0.9) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Custom Fragrance Profile */
.layton-notes.custom-fragrance-profile {
    width: 320px;
    margin: 170px 0 0 20px;
    display: inline-block;
    opacity: 0;
    transform: translateX(100px) scale(0.8);
    transition: none;
    will-change: transform, opacity;
    cursor: pointer;
}

.layton-notes.parallax-active {
    transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

.fragrance-profile-container {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.85), rgba(20, 20, 20, 0.9));
    backdrop-filter: blur(15px);
    border: 1px solid rgba(212, 175, 55, 0.3);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.layton-notes:hover .fragrance-profile-container {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.6);
    border-color: rgba(212, 175, 55, 0.5);
    transition: all 0.4s ease;
}

.profile-header {
    text-align: center;
    margin-bottom: 20px;
}

.profile-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #d4af37;
    margin: 0 0 5px 0;
    letter-spacing: 2px;
    text-transform: uppercase;
}

.profile-subtitle {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
    letter-spacing: 1px;
}

.scent-categories {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 25px;
}

.category-item {
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
}

.category-item:hover {
    transform: translateX(5px);
}

.category-bar {
    flex: 1;
    height: 24px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    position: relative;
}

.category-fill {
    height: 100%;
    border-radius: 12px;
    transition: width 1.5s ease-out;
    position: relative;
    overflow: hidden;
}

.category-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Category-specific colors */
.warm-spicy .category-fill { background: linear-gradient(90deg, #ff6b35, #ff8e53); }
.fresh-spicy .category-fill { background: linear-gradient(90deg, #7cb342, #8bc34a); }
.woody .category-fill { background: linear-gradient(90deg, #8d6e63, #a1887f); }
.vanilla .category-fill { background: linear-gradient(90deg, #fff3e0, #ffcc80); }
.aromatic .category-fill { background: linear-gradient(90deg, #26a69a, #4db6ac); }
.fruity .category-fill { background: linear-gradient(90deg, #ff7043, #ff8a65); }
.powdery .category-fill { background: linear-gradient(90deg, #e1bee7, #ce93d8); }
.lavender .category-fill { background: linear-gradient(90deg, #9575cd, #b39ddb); }
.citrus .category-fill { background: linear-gradient(90deg, #ffeb3b, #fff176); }
.fresh .category-fill { background: linear-gradient(90deg, #4fc3f7, #81d4fa); }

.category-label {
    min-width: 80px;
    font-size: 0.85rem;
    color: #ffffff;
    font-weight: 500;
    text-align: right;
    letter-spacing: 0.5px;
}

.intensity-meter {
    border-top: 1px solid rgba(212, 175, 55, 0.3);
    padding-top: 20px;
}

.meter-label {
    text-align: center;
    font-size: 0.9rem;
    color: #d4af37;
    font-weight: 600;
    margin-bottom: 15px;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.meter-container {
    position: relative;
}

.meter-track {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    position: relative;
    margin-bottom: 10px;
}

.meter-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #ff9800, #f44336);
    border-radius: 4px;
    transition: width 2s ease-out;
    position: relative;
}

.meter-indicator {
    position: absolute;
    top: -2px;
    width: 12px;
    height: 12px;
    background: #d4af37;
    border-radius: 50%;
    border: 2px solid #ffffff;
    transform: translateX(-50%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.meter-scale {
    display: flex;
    justify-content: space-between;
    font-size: 0.7rem;
    color: rgba(255, 255, 255, 0.6);
}

.scale-mark {
    font-weight: 500;
}

/* Quality Corner Ornaments */
.quality-ornament {
    position: absolute;
    width: 6px;
    height: 6px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    transition: all 0.3s ease;
}

.quality-ornament.top-left {
    top: 4px;
    left: 4px;
    border-right: none;
    border-bottom: none;
}

.quality-ornament.top-right {
    top: 4px;
    right: 4px;
    border-left: none;
    border-bottom: none;
}

.quality-ornament.bottom-left {
    bottom: 4px;
    left: 4px;
    border-right: none;
    border-top: none;
}

.quality-ornament.bottom-right {
    bottom: 4px;
    right: 4px;
    border-left: none;
    border-top: none;
}

.quality-badge:hover .quality-ornament {
    border-color: rgba(255, 255, 255, 0.7);
    transform: scale(1.2);
}

.quality-option input[type="radio"]:checked + .quality-label .quality-ornament {
    border-color: #d4af37;
    transform: scale(1.3);
}

/* Top Quality - Green Ornaments */
.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .quality-ornament {
    border-color: #2ecc71 !important;
}

/* Identical Quality - Gold Ornaments */
.quality-option[data-quality="identical"] input[type="radio"]:checked + .quality-label .quality-ornament {
    border-color: #d4af37 !important;
}

/* Additional Top Quality Green Styling - High Specificity */
.quality-option[data-quality="top"] input#top-quality:checked + label .quality-badge {
    border-color: #2ecc71 !important;
    box-shadow:
        0 6px 20px rgba(46, 204, 113, 0.4),
        0 0 30px rgba(46, 204, 113, 0.4),
        inset 0 1px 0 rgba(46, 204, 113, 0.3),
        inset 0 0 20px rgba(46, 204, 113, 0.1) !important;
}

.quality-option[data-quality="top"] input#top-quality:checked + label .quality-name {
    color: #2ecc71 !important;
    text-shadow:
        0 1px 3px rgba(0, 0, 0, 0.4),
        0 0 8px rgba(46, 204, 113, 0.5) !important;
}

.quality-option[data-quality="top"] input#top-quality:checked + label .quality-ornament {
    border-color: #2ecc71 !important;
}

/* Quality Shimmer Effect */
.quality-shimmer {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: qualityShimmer 4s infinite;
    pointer-events: none;
}

@keyframes qualityShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Quality Glow Effect */
.quality-glow {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: radial-gradient(circle, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
    border-radius: 15px;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

.quality-option input[type="radio"]:checked + .quality-label .quality-glow {
    opacity: 0.8;
    animation: qualityPulse 2s ease-in-out infinite;
}

/* Top Quality - Green Glow */
.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .quality-glow {
    background: radial-gradient(circle, rgba(46, 204, 113, 0.2) 0%, transparent 70%);
    animation: qualityPulseGreen 2s ease-in-out infinite;
}

/* Identical Quality - Gold Glow */
.quality-option[data-quality="identical"] input[type="radio"]:checked + .quality-label .quality-glow {
    background: radial-gradient(circle, rgba(212, 175, 55, 0.2) 0%, transparent 70%) !important;
    animation: qualityPulse 2s ease-in-out infinite !important;
}

@keyframes qualityPulse {
    0%, 100% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

@keyframes qualityPulseGreen {
    0%, 100% { opacity: 0.4; transform: scale(1); }
    50% { opacity: 0.8; transform: scale(1.05); }
}

/* Selection Indicator */
.selection-indicator {
    position: absolute;
    top: 50%;
    right: 15px;
    width: 12px;
    height: 12px;
    border: 2px solid rgba(212, 175, 55, 0.3);
    border-radius: 50%;
    transform: translateY(-50%);
    transition: all 0.3s ease;
    background: transparent;
}

.selection-indicator::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 6px;
    height: 6px;
    background: #d4af37;
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 0 8px rgba(212, 175, 55, 0.6);
}

.quality-option input[type="radio"]:checked + .quality-label .selection-indicator {
    border-color: #d4af37;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.4);
}

.quality-option input[type="radio"]:checked + .quality-label .selection-indicator::after {
    transform: translate(-50%, -50%) scale(1);
}

/* Top Quality - Green Selection Indicator */
.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .selection-indicator {
    border-color: #2ecc71 !important;
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.4) !important;
}

.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .selection-indicator::after {
    background: #2ecc71 !important;
    box-shadow: 0 0 8px rgba(46, 204, 113, 0.6) !important;
}

/* Identical Quality - Gold Selection Indicator */
.quality-option[data-quality="identical"] input[type="radio"]:checked + .quality-label .selection-indicator {
    border-color: #d4af37 !important;
    box-shadow: 0 0 10px rgba(212, 175, 55, 0.4) !important;
}

.quality-option[data-quality="identical"] input[type="radio"]:checked + .quality-label .selection-indicator::after {
    background: #d4af37 !important;
    box-shadow: 0 0 8px rgba(212, 175, 55, 0.6) !important;
}

/* Top Quality - Green Selection Indicator */
.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .selection-indicator {
    border-color: #2ecc71;
    box-shadow: 0 0 10px rgba(46, 204, 113, 0.4);
}

.quality-option[data-quality="top"] input[type="radio"]:checked + .quality-label .selection-indicator::after {
    background: #2ecc71;
    box-shadow: 0 0 8px rgba(46, 204, 113, 0.6);
}

/* Subtle Floating Animation for Quality Options */
.quality-badge {
    animation: qualityFloat 6s ease-in-out infinite;
}

.quality-option:nth-child(2) .quality-badge {
    animation-delay: -3s;
}

@keyframes qualityFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-2px); }
}

.quality-badge:hover {
    animation: none;
}

/* Fragrance Notes Section */
.fragrance-notes {
    display: inline-block;
    vertical-align: top;
    margin: 170px 0 0 30px;
    opacity: 0;
    transform: translateX(150px) scale(0.8);
    transition: none;
    will-change: transform, opacity;
    max-width: 400px;
}

.fragrance-notes.parallax-active {
    transition: transform 0.1s ease-out, opacity 0.1s ease-out;
}

.notes-section {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 30px;
    color: white;
}

.notes-title {
    font-family: 'Playfair Display', serif;
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    text-align: center;
    margin: 30px 0 20px 0;
    letter-spacing: 1px;
}

.notes-title:first-child {
    margin-top: 0;
}

.notes-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    justify-content: center;
    margin-bottom: 10px;
}

.note-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 80px;
    transition: transform 0.3s ease, filter 0.3s ease;
}

.note-item:hover {
    transform: translateY(-5px) scale(1.1);
    filter: brightness(1.3);
}

.note-icon {
    font-size: 32px;
    margin-bottom: 8px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.note-name {
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    letter-spacing: 0.5px;
    line-height: 1.2;
}

/* Perfume Rating Section */
.perfume-rating {
    display: block;
    margin: 50px auto 0 auto;
    max-width: 800px;
    padding: 0 20px;
    opacity: 0;
    transform: translateY(50px);
    transition: none;
    will-change: transform, opacity;
}

.perfume-rating.parallax-active {
    transition: transform 0.8s ease-out, opacity 0.8s ease-out;
}

.rating-indicators {
    display: flex;
    flex-direction: column;
    gap: 30px;
    margin-bottom: 40px;
}

.mood-indicators,
.season-indicators {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.indicator-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 60px;
    cursor: pointer;
    transition: transform 0.3s ease, filter 0.3s ease;
}

.indicator-item:hover {
    transform: translateY(-3px) scale(1.05);
    filter: brightness(1.2);
}

.indicator-item.active .indicator-icon {
    transform: scale(1.2);
    filter: brightness(1.3) drop-shadow(0 0 10px rgba(255, 255, 255, 0.5));
}

.indicator-icon {
    font-size: 28px;
    margin-bottom: 8px;
    transition: transform 0.3s ease, filter 0.3s ease;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.indicator-label {
    font-family: 'Inter', sans-serif;
    font-size: 12px;
    font-weight: 500;
    color: #ffffff;
    text-align: center;
    letter-spacing: 0.5px;
    margin-bottom: 6px;
}

.indicator-bar {
    width: 40px;
    height: 3px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.indicator-item.active .indicator-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, #ff6b6b, #ff8e8e);
    border-radius: 2px;
    animation: fillBar 1s ease-out 0.5s both;
}

/* Season-specific bar colors */
.winter-bar::after {
    background: linear-gradient(90deg, #74c0fc, #a5d8ff) !important;
}

.spring-bar::after {
    background: linear-gradient(90deg, #8ce99a, #b2f2bb) !important;
}

.summer-bar::after {
    background: linear-gradient(90deg, #ffd43b, #fff3bf) !important;
}

.fall-bar::after {
    background: linear-gradient(90deg, #ffa94d, #ffc078) !important;
}

.day-bar::after {
    background: linear-gradient(90deg, #ffd43b, #fff3bf) !important;
}

.night-bar::after {
    background: linear-gradient(90deg, #495057, #6c757d) !important;
}

@keyframes fillBar {
    from {
        width: 0%;
    }
    to {
        width: 100%;
    }
}

.rating-score {
    text-align: center;
    margin-bottom: 30px;
}

.rating-title {
    font-family: 'Inter', sans-serif;
    font-size: 18px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.4;
}

.rating-title .score {
    font-weight: 700;
    color: #ffd43b;
}

.rating-title .votes {
    font-weight: 600;
    color: #74c0fc;
}

.perfume-description {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
}

.perfume-description p {
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
    line-height: 1.6;
    margin: 0;
}

.perfume-description strong {
    font-weight: 600;
    color: #ffd43b;
}

/* Additional Ratings Sections */
.additional-ratings {
    margin: 0px 0 40px 0;
}

.rating-row {
    display: flex;
    gap: 20px;
    margin-bottom: 40px;
}

.rating-category {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 25px;
    margin: 0 10px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    justify-content: center;
}

.category-icon {
    font-size: 20px;
    opacity: 0.7;
}

.category-title {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 1px;
    margin: 0;
}

.no-vote {
    color: rgba(255, 255, 255, 0.5);
    font-size: 12px;
    margin-left: auto;
}

.rating-bars {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rating-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

.rating-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 13px;
    min-width: 80px;
    text-align: left;
}

.rating-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.bar-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

.rating-count {
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    min-width: 40px;
    text-align: right;
}

/* Gender Scale */
.gender-indicators {
    margin-bottom: 15px;
}

.gender-scale {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.gender-labels {
    display: flex;
    justify-content: space-between;
    width: 100%;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
}

.gender-dots {
    display: flex;
    gap: 8px;
}

.gender-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.gender-dot.active {
    background: #4a9eff;
    box-shadow: 0 0 8px rgba(74, 158, 255, 0.5);
}

/* Price Scale */
.price-indicators {
    margin-bottom: 15px;
}

.price-scale {
    display: flex;
    justify-content: center;
}

.price-labels {
    display: flex;
    gap: 40px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    font-weight: 600;
}

/* Responsive adjustments for Layton images and notes */
@media (max-width: 768px) {
    .layton-image {
        width: 250px;
        margin: 40px 0 0 40px;
    }

    .layton-notes.custom-fragrance-profile {
        width: 280px;
        margin: 80px 0 0 15px; /* Closer spacing on tablet */
    }

    .fragrance-profile-container {
        padding: 20px;
    }

    .profile-title {
        font-size: 1.2rem;
        letter-spacing: 1.5px;
    }

    .profile-subtitle {
        font-size: 0.8rem;
    }

    .category-bar {
        height: 20px;
    }

    .category-label {
        min-width: 70px;
        font-size: 0.8rem;
    }

    .meter-label {
        font-size: 0.8rem;
    }

    .meter-scale {
        font-size: 0.65rem;
    }

    .fragrance-notes {
        margin: 80px 0 0 20px;
        max-width: 300px;
    }

    .product-title {
        margin: 120px 0 0 280px;
    }

    .brand-name {
        font-size: 18px;
        letter-spacing: 3px;
        margin-bottom: 4px;
    }

    .brand-location {
        font-size: 12px;
        letter-spacing: 2px;
        margin-bottom: 20px;
    }

    .product-name {
        font-size: 18px;
        letter-spacing: 3px;
        margin-bottom: 10px;
    }

    /* Tablet Price Styling */
    .price-badge {
        padding: 12px 20px;
        border-radius: 12px;
    }

    .price-currency {
        font-size: 26px;
    }

    .price-unit {
        font-size: 16px;
    }

    .price-badge:hover .price-currency {
        font-size: 28px;
    }

    .price-subtitle {
        font-size: 11px;
        letter-spacing: 1.5px;
    }

    .price-ornament {
        width: 6px;
        height: 6px;
    }

    /* Tablet Quality Selector */
    .quality-selector-container {
        margin-top: 20px;
        gap: 12px;
    }

    .quality-title {
        font-size: 16px;
        letter-spacing: 1.5px;
    }

    .quality-subtitle {
        font-size: 10px;
        letter-spacing: 1px;
    }

    .quality-options {
        max-width: 260px;
        gap: 10px;
    }

    .quality-badge {
        padding: 10px 16px;
        border-radius: 10px;
    }

    .quality-name {
        font-size: 14px;
    }

    .quality-description {
        font-size: 10px;
    }

    .selection-indicator {
        width: 10px;
        height: 10px;
        right: 12px;
    }

    .selection-indicator::after {
        width: 5px;
        height: 5px;
    }

    .quality-ornament {
        width: 5px;
        height: 5px;
    }

    .notes-section {
        padding: 20px;
    }

    .notes-title {
        font-size: 20px;
        margin: 20px 0 15px 0;
    }

    .note-icon {
        font-size: 28px;
    }

    .note-name {
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .layton-image {
        width: 200px;
        margin: 30px 0 0 30px;
    }

    .layton-notes.custom-fragrance-profile {
        width: 240px;
        margin: 65px 0 0 10px; /* Closer spacing on mobile */
    }

    .fragrance-profile-container {
        padding: 15px;
    }

    .profile-title {
        font-size: 1rem;
        letter-spacing: 1px;
    }

    .profile-subtitle {
        font-size: 0.75rem;
    }

    .category-bar {
        height: 18px;
    }

    .category-label {
        min-width: 60px;
        font-size: 0.75rem;
    }

    .meter-label {
        font-size: 0.75rem;
    }

    .meter-scale {
        font-size: 0.6rem;
    }

    .meter-indicator {
        width: 10px;
        height: 10px;
    }

    .fragrance-notes {
        margin: 65px 0 0 15px;
        max-width: 250px;
    }

    .notes-section {
        padding: 15px;
    }

    .notes-title {
        font-size: 18px;
        margin: 15px 0 10px 0;
    }

    .notes-row {
        gap: 15px;
    }

    .note-item {
        min-width: 60px;
    }

    .note-icon {
        font-size: 24px;
        margin-bottom: 6px;
    }

    .note-name {
        font-size: 11px;
    }

    .perfume-rating {
        margin: 30px auto 0 auto;
        padding: 0 15px;
    }

    .rating-indicators {
        gap: 20px;
        margin-bottom: 30px;
    }

    .mood-indicators,
    .season-indicators {
        gap: 15px;
    }

    .indicator-item {
        min-width: 50px;
    }

    .indicator-icon {
        font-size: 24px;
        margin-bottom: 6px;
    }

    .indicator-label {
        font-size: 11px;
    }

    .indicator-bar {
        width: 35px;
        height: 2px;
    }

    .rating-title {
        font-size: 16px;
    }

    .perfume-description {
        padding: 20px;
    }

    .perfume-description p {
        font-size: 14px;
        line-height: 1.5;
    }

    .brand-image-section {
        height: 50vh;
    }

    .brand-image {
        max-width: 85%;
        max-height: 85%;
    }

    .additional-ratings {
        margin: 0px 0 30px 0;
    }

    .rating-row {
        flex-direction: column;
        gap: 25px;
    }

    .rating-category {
        padding: 20px;
    }

    .category-title {
        font-size: 13px;
    }

    .rating-label {
        min-width: 70px;
        font-size: 12px;
    }

    .product-title {
        margin: 90px 0 0 280px;
    }

    .brand-name {
        font-size: 14px;
        letter-spacing: 2px;
        margin-bottom: 3px;
    }

    .brand-location {
        font-size: 10px;
        letter-spacing: 1.5px;
        margin-bottom: 15px;
    }

    .product-name {
        font-size: 14px;
        letter-spacing: 2px;
        margin-bottom: 8px;
    }

    /* Mobile Price Styling */
    .product-price-container {
        margin-top: 15px;
        gap: 6px;
    }

    .price-badge {
        padding: 10px 18px;
        border-radius: 10px;
    }

    .price-currency {
        font-size: 22px;
    }

    .price-unit {
        font-size: 14px;
    }

    .price-badge:hover .price-currency {
        font-size: 24px;
    }

    .price-subtitle {
        font-size: 10px;
        letter-spacing: 1px;
    }

    .price-ornament {
        width: 5px;
        height: 5px;
    }

    /* Reduce animation intensity on mobile */
    .price-badge:hover {
        transform: translateY(-2px) scale(1.02);
    }

    @keyframes priceFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-3px); }
    }

    /* Mobile Quality Selector */
    .quality-selector-container {
        margin-top: 18px;
        gap: 10px;
    }

    .quality-title {
        font-size: 14px;
        letter-spacing: 1px;
    }

    .quality-subtitle {
        font-size: 9px;
        letter-spacing: 0.8px;
    }

    .quality-options {
        max-width: 240px;
        gap: 8px;
    }

    .quality-badge {
        padding: 8px 14px;
        border-radius: 8px;
    }

    .quality-name {
        font-size: 13px;
        letter-spacing: 0.8px;
    }

    .quality-description {
        font-size: 9px;
    }

    .selection-indicator {
        width: 9px;
        height: 9px;
        right: 10px;
    }

    .selection-indicator::after {
        width: 4px;
        height: 4px;
    }

    .quality-ornament {
        width: 4px;
        height: 4px;
    }

    /* Reduce animation intensity on mobile */
    .quality-badge:hover {
        transform: translateY(-1px) scale(1.01);
    }

    @keyframes qualityFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-1px); }
    }

    .brand-image-section {
        height: 40vh;
    }

    .brand-image {
        max-width: 90%;
        max-height: 90%;
    }

    .additional-ratings {
        margin: 8px 0 25px 0;
    }

    .rating-row {
        gap: 20px;
    }

    .rating-category {
        padding: 18px;
    }

    .category-header {
        gap: 8px;
    }

    .category-icon {
        font-size: 18px;
    }

    .category-title {
        font-size: 12px;
    }

    .rating-label {
        min-width: 60px;
        font-size: 11px;
    }

    .rating-count {
        font-size: 11px;
        min-width: 35px;
    }

    .gender-labels {
        font-size: 11px;
    }

    .gender-dot {
        width: 10px;
        height: 10px;
    }

    .price-labels {
        gap: 30px;
        font-size: 11px;
    }
}



.content {
    min-height: 300vh;
}

.final {
    min-height: 100vh;
}

html {
    scroll-behavior: smooth;
}